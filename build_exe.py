#!/usr/bin/env python3
"""
Military Threat Detection System - EXE Builder for Visual Studio 2022
Creates standalone Windows executable using PyInstaller
"""

import os
import sys
import subprocess
from pathlib import Path

def build_exe():
    """Build standalone EXE for Windows"""
    print("Building Military Threat Detection System EXE...")
    print("=" * 60)

    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("PyInstaller found")
    except ImportError:
        print("PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("PyInstaller installed")
    
    # Check if icon exists, create if needed
    icon_path = "assets/icon.ico"
    use_icon = False

    if not os.path.exists("assets"):
        os.makedirs("assets", exist_ok=True)

    if not os.path.exists(icon_path):
        print(f"⚠️  Icon not found: {icon_path}")
        print("🎨 Creating Israeli flag icon...")
        try:
            # Try to create icon
            from create_icon import create_icon
            create_icon()
            if os.path.exists(icon_path):
                use_icon = True
                print(f"Icon created: {icon_path}")
        except Exception as e:
            print(f"Could not create icon: {e}")
            print("Building without custom icon...")
    else:
        use_icon = True
        print(f"Icon found: {icon_path}")

    # Build command - start with basic options
    build_cmd = [
        "pyinstaller",
        "--onefile",                    # Single EXE file
        "--windowed",                   # No console window
        "--name=MilitaryThreatDetection",   # EXE name
        "--clean",                      # Clean build
    ]

    # Add icon if available
    if use_icon and os.path.exists(icon_path):
        build_cmd.append(f"--icon={icon_path}")
        print(f"Using icon: {icon_path}")
    else:
        print("Building without custom icon (using default)")

    # Add data directories only if they exist
    data_dirs = [
        ("config", "config"),
        ("models", "models"),
        ("data", "data")
    ]

    for src, dst in data_dirs:
        if os.path.exists(src):
            build_cmd.append(f"--add-data={src};{dst}")
            print(f"Including directory: {src}")
        else:
            print(f"Directory not found, skipping: {src}")

    # Add hidden imports
    hidden_imports = [
        "PyQt5",
        "PyQt5.QtCore",
        "PyQt5.QtGui",
        "PyQt5.QtWidgets",
        "sklearn",
        "torch",
        "onnxruntime",
        "PIL",
        "yaml",
        "numpy",
        "cv2"
    ]

    for imp in hidden_imports:
        build_cmd.append(f"--hidden-import={imp}")

    # Add main file
    build_cmd.append("gui_main.py")
    
    print("🔨 Building EXE with PyInstaller...")
    print(f"Command: {' '.join(build_cmd)}")
    print()
    
    try:
        result = subprocess.run(build_cmd, check=True, capture_output=True, text=True)
        print("Build successful!")
        print()
        print("Output location: dist/MilitaryThreatDetection.exe")
        print("File size: ~100-200MB (includes all dependencies)")
        print()
        print("Ready to deploy on any Windows PC!")
        print("Military threat detection system ready for deployment")

    except subprocess.CalledProcessError as e:
        print("Build failed!")
        print(f"Error: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error output: {e.stderr}")

        # Try fallback build without icon
        print("\nTrying fallback build without icon...")
        fallback_cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name=MilitaryThreatDetection",
            "--clean",
            "gui_main.py"
        ]

        try:
            result = subprocess.run(fallback_cmd, check=True, capture_output=True, text=True)
            print("Fallback build successful!")
            print("Output location: dist/MilitaryThreatDetection.exe")
            print("Built without icon and extra data directories")
            return True
        except subprocess.CalledProcessError as e2:
            print("Fallback build also failed!")
            print(f"Error: {e2}")
            if e2.stderr:
                print(f"Error output: {e2.stderr}")
            return False
    
    return True

def create_installer():
    """Create Windows installer using NSIS (optional)"""
    print("\n🎁 Creating Windows Installer...")
    
    nsis_script = """
; Israel Defense Forces Threat Detection Installer
!define APPNAME "Israel Defense Forces - Threat Detection"
!define COMPANYNAME "Israel Defense Forces"
!define DESCRIPTION "AI-powered threat detection system for protecting Israel"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0

!include "MUI2.nsh"

Name "${APPNAME}"
OutFile "IsraelDefenseForces_Setup.exe"
InstallDir "$PROGRAMFILES\\${COMPANYNAME}\\${APPNAME}"
RequestExecutionLevel admin

!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

!insertmacro MUI_LANGUAGE "English"

Section "Install"
    SetOutPath $INSTDIR
    File "dist\\IsraelDefenseForces.exe"
    File /r "config"
    File /r "models"
    File /r "data"
    
    CreateDirectory "$SMPROGRAMS\\${COMPANYNAME}"
    CreateShortCut "$SMPROGRAMS\\${COMPANYNAME}\\${APPNAME}.lnk" "$INSTDIR\\IsraelDefenseForces.exe"
    CreateShortCut "$DESKTOP\\Israel Defense Forces.lnk" "$INSTDIR\\IsraelDefenseForces.exe"
    
    WriteUninstaller "$INSTDIR\\Uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\\IsraelDefenseForces.exe"
    Delete "$INSTDIR\\Uninstall.exe"
    RMDir /r "$INSTDIR"
    Delete "$SMPROGRAMS\\${COMPANYNAME}\\${APPNAME}.lnk"
    Delete "$DESKTOP\\Israel Defense Forces.lnk"
    RMDir "$SMPROGRAMS\\${COMPANYNAME}"
SectionEnd
"""
    
    with open("installer.nsi", "w") as f:
        f.write(nsis_script)
    
    print("📝 NSIS installer script created: installer.nsi")
    print("💡 To build installer: Install NSIS and run 'makensis installer.nsi'")

if __name__ == "__main__":
    success = build_exe()
    if success:
        create_installer()
        print("\nBuild complete! Ready for deployment.")
    else:
        print("\nBuild failed. Check errors above.")
        sys.exit(1)
