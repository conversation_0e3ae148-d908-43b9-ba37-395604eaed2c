#!/usr/bin/env python3
"""
Debug script to analyze actual embedding values from your images
to fix the classification thresholds
"""

import sys
from pathlib import Path
import numpy as np

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

def analyze_image_embeddings():
    """Analyze the actual embeddings from your problem images"""
    print("🔍 EMBEDDING ANALYSIS - Debugging Classification Issue")
    print("=" * 70)
    
    try:
        from classifier.classifier import classify_image_direct_onnx
        
        # Test images that are showing the same results
        test_images = [
            "data/input_images/Shahed_136_0610152327_texture.png",  # Should be Shahed-136
            "data/input_images/test.jpg"  # Different weapon
        ]
        
        # Add any other images if they exist
        input_folder = Path("data/input_images")
        if input_folder.exists():
            for ext in ['.webp', '.jpg', '.png']:
                test_images.extend([str(p) for p in input_folder.glob(f"*{ext}")])
        
        # Remove duplicates
        test_images = list(set(test_images))
        
        print(f"📷 Found {len(test_images)} images to analyze:")
        for img in test_images:
            if Path(img).exists():
                print(f"   ✅ {Path(img).name}")
            else:
                print(f"   ❌ {Path(img).name} (not found)")
        
        print("\n" + "=" * 70)
        
        # Analyze each image's embeddings
        for i, image_path in enumerate(test_images):
            if not Path(image_path).exists():
                continue
                
            print(f"\n📊 IMAGE {i+1}: {Path(image_path).name}")
            print("-" * 50)
            
            try:
                # Get the raw embeddings by calling the ONNX model directly
                from classifier.classifier import classify_image_direct_onnx
                import onnxruntime as ort
                from PIL import Image
                import torchvision.transforms as transforms
                from config import config_loader
                
                # Load ONNX model
                config = config_loader.load_config()
                onnx_model_path = config['paths']['onnx_model']
                
                session_options = ort.SessionOptions()
                session_options.enable_cpu_mem_arena = False
                session_options.enable_mem_pattern = False
                
                session = ort.InferenceSession(
                    onnx_model_path,
                    providers=['CPUExecutionProvider'],
                    sess_options=session_options
                )
                
                # Preprocess image
                img = Image.open(image_path).convert("RGB")
                transform = transforms.Compose([
                    transforms.Resize((240, 240)),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
                ])
                img_tensor = transform(img).unsqueeze(0).numpy()
                
                # Get embeddings
                input_name = session.get_inputs()[0].name
                outputs = session.run(None, {input_name: img_tensor})
                embedding = outputs[0][0]  # Shape: [640]
                
                # Calculate all the statistics used in classification
                embedding_mean = np.mean(embedding)
                embedding_std = np.std(embedding)
                embedding_max = np.max(embedding)
                embedding_min = np.min(embedding)
                embedding_sum = np.sum(embedding)
                embedding_range = embedding_max - embedding_min
                positive_activations = np.sum(embedding > 0)
                negative_activations = np.sum(embedding < 0)
                embedding_median = np.median(embedding)
                embedding_var = np.var(embedding)
                embedding_skew = np.mean(((embedding - embedding_mean) / embedding_std) ** 3) if embedding_std > 0 else 0
                embedding_kurtosis = np.mean(((embedding - embedding_mean) / embedding_std) ** 4) if embedding_std > 0 else 0
                high_activation_count = np.sum(embedding > embedding_mean + embedding_std)
                low_activation_count = np.sum(embedding < embedding_mean - embedding_std)
                
                print(f"📈 EMBEDDING STATISTICS:")
                print(f"   Mean: {embedding_mean:.6f}")
                print(f"   Std:  {embedding_std:.6f}")
                print(f"   Max:  {embedding_max:.6f}")
                print(f"   Min:  {embedding_min:.6f}")
                print(f"   Sum:  {embedding_sum:.6f}")
                print(f"   Range: {embedding_range:.6f}")
                print(f"   Median: {embedding_median:.6f}")
                print(f"   Variance: {embedding_var:.6f}")
                print(f"   Skewness: {embedding_skew:.6f}")
                print(f"   Kurtosis: {embedding_kurtosis:.6f}")
                print(f"   Positive activations: {positive_activations}")
                print(f"   Negative activations: {negative_activations}")
                print(f"   High activation count: {high_activation_count}")
                print(f"   Low activation count: {low_activation_count}")
                
                # Show which classification threshold this would hit
                print(f"\n🎯 CLASSIFICATION ANALYSIS:")
                if embedding_mean > 0.2 and embedding_std > 0.3 and embedding_kurtosis > 2:
                    print("   ✅ Would hit: Very high activation (Ballistic missiles)")
                elif embedding_mean > 0.15 and embedding_range > 1.2 and high_activation_count > 50:
                    print("   ✅ Would hit: High activation (Cruise missiles)")
                elif embedding_mean > 0.1 and embedding_std > 0.18 and embedding_skew < -0.5:
                    print("   ✅ Would hit: Medium-high activation (Precision missiles)")
                elif embedding_mean > 0.08 and embedding_max > 0.6 and low_activation_count < 100:
                    print("   ✅ Would hit: Medium activation (Drones)")
                elif embedding_mean > 0.05 and embedding_range > 0.8:
                    print("   ✅ Would hit: Lower-medium activation (Rockets)")
                elif embedding_mean > 0.03 and embedding_std > 0.08:
                    print("   ✅ Would hit: Lower activation (Small weapons)")
                else:
                    print("   ❌ Falls to DEFAULT case (Very low activation)")
                    print("   🚨 This is why both images show 'Shahed-136 (50.0%)'!")
                
                # Test the actual classification
                result, confidence, class_probs = classify_image_direct_onnx(image_path, return_confidence=True)
                print(f"\n🚀 ACTUAL CLASSIFICATION: {result} ({confidence:.1%})")
                
            except Exception as e:
                print(f"❌ Error analyzing {Path(image_path).name}: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n" + "=" * 70)
        print("💡 RECOMMENDED FIXES:")
        print("1. Lower the classification thresholds to match actual embedding ranges")
        print("2. Add more granular conditions for very low activation embeddings")
        print("3. Use relative thresholds based on embedding statistics")
        print("=" * 70)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_image_embeddings()
