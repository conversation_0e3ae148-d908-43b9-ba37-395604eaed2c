name: 🇮🇱 Israel Defense Forces - CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    name: Test on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: [3.8, 3.9, '3.10', '3.11']

    steps:
    - name: 🇮🇱 Checkout Israel Defense Code
      uses: actions/checkout@v4

    - name: 🐍 Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: 📦 Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: 🧪 Run PC Compatibility Test
      run: |
        python test_pc_compatibility.py

    - name: 🎯 Run REDFOR Detection Test
      run: |
        python test_redfor.py

    - name: 🔍 Test GUI Import (Linux/macOS)
      if: matrix.os != 'windows-latest'
      run: |
        python -c "import sys; sys.path.append('.'); from gui_main import ThreatDetectionGUI; print('✅ GUI imports successfully')"

    - name: 🏗️ Test Build System
      run: |
        python -c "import build_exe; print('✅ Build system ready')"

  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    steps:
    - name: 🇮🇱 Checkout Israel Defense Code
      uses: actions/checkout@v4

    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: 🔍 Run Security Scan
      run: |
        pip install bandit safety
        bandit -r . -f json || true
        safety check || true

  build:
    name: 🏗️ Build Test
    runs-on: windows-latest
    needs: test
    steps:
    - name: 🇮🇱 Checkout Israel Defense Code
      uses: actions/checkout@v4

    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: 📦 Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller

    - name: 🎨 Create Icon
      run: |
        python create_icon.py

    - name: 🏗️ Test Minimal Build
      run: |
        python build_minimal.py

    - name: 📊 Check Build Artifacts
      run: |
        if (Test-Path "dist/IsraelDefenseForces.exe") {
          Write-Host "✅ EXE build successful"
          $size = (Get-Item "dist/IsraelDefenseForces.exe").Length / 1MB
          Write-Host "📦 EXE size: $([math]::Round($size, 1)) MB"
        } else {
          Write-Host "❌ EXE build failed"
          exit 1
        }
