#!/usr/bin/env python3
"""
Test script to verify ONNX Runtime DLL fix
Tests the image analysis functionality that was failing before
"""

import sys
import os
from pathlib import Path

def test_onnx_import():
    """Test ONNX Runtime import"""
    print("🧪 Testing ONNX Runtime import...")
    try:
        import onnxruntime as ort
        print(f"✅ ONNX Runtime {ort.__version__} imported successfully")
        
        providers = ort.get_available_providers()
        print(f"✅ Available providers: {providers}")
        
        # Test session options (the ones that fix DLL issues)
        session_options = ort.SessionOptions()
        session_options.enable_cpu_mem_arena = False
        session_options.enable_mem_pattern = False
        print("✅ Session options configured successfully")
        
        return True
    except Exception as e:
        print(f"❌ ONNX Runtime test failed: {e}")
        return False

def test_feature_extractor():
    """Test the feature extractor module"""
    print("\n🧪 Testing feature extractor...")
    try:
        from classifier.feature_extractor import load_onnx_model, extract_features
        print("✅ Feature extractor imported successfully")
        
        # Test model loading (will show warning if no model, but shouldn't crash)
        session = load_onnx_model()
        if session:
            print("✅ ONNX model loaded successfully")
        else:
            print("⚠️  No ONNX model found (this is OK for testing)")
        
        return True
    except Exception as e:
        print(f"❌ Feature extractor test failed: {e}")
        return False

def test_classifier():
    """Test the classifier module"""
    print("\n🧪 Testing classifier...")
    try:
        from classifier.classifier import classify_image, get_weapon_info
        print("✅ Classifier imported successfully")
        return True
    except Exception as e:
        print(f"❌ Classifier test failed: {e}")
        return False

def test_gui_imports():
    """Test GUI imports"""
    print("\n🧪 Testing GUI imports...")
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QThread, pyqtSignal
        print("✅ PyQt5 imported successfully")
        
        # Test if we can import the main GUI
        from gui_main import ThreatDetectionGUI, AnalysisThread
        print("✅ GUI modules imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ GUI import test failed: {e}")
        return False

def test_sample_image():
    """Test with a sample image if available"""
    print("\n🧪 Testing sample image analysis...")
    
    # Look for sample images
    image_folders = [
        Path("data/input_images"),
        Path("data/train_images"),
        Path(".")
    ]
    
    sample_image = None
    for folder in image_folders:
        if folder.exists():
            for ext in [".jpg", ".jpeg", ".png"]:
                images = list(folder.glob(f"*{ext}"))
                if images:
                    sample_image = images[0]
                    break
            if sample_image:
                break
    
    if not sample_image:
        print("⚠️  No sample images found - skipping image analysis test")
        return True
    
    print(f"📸 Found sample image: {sample_image}")
    
    try:
        # Test if we can at least try to analyze (may fail due to no trained model)
        from classifier.classifier import classify_image
        
        # This might fail due to no model, but shouldn't crash with DLL error
        try:
            result = classify_image(str(sample_image))
            print(f"✅ Image analysis successful: {result}")
        except FileNotFoundError as e:
            if "model" in str(e).lower():
                print("⚠️  No trained model found (this is OK for testing)")
                print("✅ No DLL errors - ONNX Runtime is working!")
            else:
                raise
        except Exception as e:
            if "DLL" in str(e) or "pybind11" in str(e):
                print(f"❌ DLL error still present: {e}")
                return False
            else:
                print(f"⚠️  Analysis failed (but not due to DLL): {e}")
                print("✅ No DLL errors - ONNX Runtime is working!")
        
        return True
        
    except Exception as e:
        if "DLL" in str(e) or "pybind11" in str(e):
            print(f"❌ DLL error still present: {e}")
            return False
        else:
            print(f"⚠️  Test failed (but not due to DLL): {e}")
            return True

def main():
    """Run all tests"""
    print("🇮🇱 Israel Defense Forces - ONNX Runtime Fix Verification")
    print("=" * 60)
    
    tests = [
        ("ONNX Runtime Import", test_onnx_import),
        ("Feature Extractor", test_feature_extractor),
        ("Classifier", test_classifier),
        ("GUI Imports", test_gui_imports),
        ("Sample Image Analysis", test_sample_image),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ ONNX Runtime DLL fix was successful")
        print("🚀 You can now run your threat detection system:")
        print("   - python gui_main.py")
        print("   - Or press F5 in Visual Studio 2022")
    else:
        print("⚠️  Some tests failed, but check if DLL errors are resolved")
        print("💡 The main issue (DLL error) should be fixed even if other tests fail")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    input("Press Enter to exit...")
    sys.exit(0 if success else 1)
