@echo off
REM 🇮🇱 Basic EXE Builder for Israel Defense Forces
echo 🇮🇱 Building Israel Defense Forces EXE...

REM Check if PyInstaller is installed
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller not found. Installing...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ Failed to install PyInstaller
        pause
        exit /b 1
    )
)

echo ✅ PyInstaller found

REM Create icon if it doesn't exist
if not exist "assets\icon.ico" (
    echo 🎨 Creating icon...
    python create_icon.py
)

REM Build EXE
echo 🔨 Building EXE...
pyinstaller --onefile --windowed --name=IsraelDefenseForces --icon=assets\icon.ico gui_main.py

if errorlevel 1 (
    echo ❌ Build failed!
    echo.
    echo 💡 Trying without icon...
    pyinstaller --onefile --windowed --name=IsraelDefenseForces gui_main.py
    
    if errorlevel 1 (
        echo ❌ Build failed again!
        echo.
        echo 🔧 Troubleshooting:
        echo   1. Install PyQt5: pip install PyQt5
        echo   2. Test GUI: python gui_main.py
        echo   3. Check dependencies
        pause
        exit /b 1
    )
)

echo ✅ Build successful!
echo.
echo 📁 Output: dist\IsraelDefenseForces.exe
echo 🚀 Ready to protect Israel!

if exist "dist\IsraelDefenseForces.exe" (
    echo.
    echo 🎯 Testing EXE...
    echo   Double-click dist\IsraelDefenseForces.exe to test
) else (
    echo ❌ EXE not found in dist folder
)

pause
