#!/usr/bin/env python3
"""
PC Compatibility Test for Israel Defense Forces Threat Detection System
Tests Windows/Mac/Linux compatibility
"""

import sys
import platform
import importlib

def test_pc_compatibility():
    """Test PC compatibility across Windows/Mac/Linux"""
    print("🖥️  PC COMPATIBILITY TEST - ISRAEL DEFENSE SYSTEM")
    print("=" * 60)
    
    # System Information
    print(f"🖥️  Operating System: {platform.system()} {platform.release()}")
    print(f"🐍 Python Version: {sys.version.split()[0]}")
    print(f"🏗️  Architecture: {platform.machine()}")
    print(f"💻 Platform: {platform.platform()}")
    
    # Test Python version
    print("\n1. 🐍 Python Version Check:")
    if sys.version_info >= (3, 8):
        print("✅ Python 3.8+ - Excellent compatibility")
    elif sys.version_info >= (3, 7):
        print("⚠️  Python 3.7 - Basic compatibility (upgrade recommended)")
    else:
        print("❌ Python < 3.7 - Upgrade required")
        return False
    
    # Test core dependencies
    print("\n2. 📦 Core Dependencies:")
    core_packages = [
        ('numpy', 'NumPy'),
        ('sklearn', 'scikit-learn'),
        ('joblib', 'Joblib'),
        ('PIL', 'Pillow'),
        ('yaml', 'PyYAML')
    ]
    
    missing_packages = []
    for package, name in core_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - Not installed")
            missing_packages.append(name)
    
    # Test AI frameworks
    print("\n3. 🤖 AI Framework Support:")
    ai_packages = [
        ('torch', 'PyTorch'),
        ('onnxruntime', 'ONNX Runtime'),
        ('tensorflow', 'TensorFlow')
    ]
    
    ai_available = []
    for package, name in ai_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {name}")
            ai_available.append(name)
        except ImportError:
            print(f"⚠️  {name} - Not installed (optional)")
    
    # Test GUI support
    print("\n4. 🖼️  GUI Support:")
    gui_packages = [
        ('tkinter', 'Tkinter (built-in)'),
        ('PyQt5', 'PyQt5'),
        ('PyQt6', 'PyQt6')
    ]
    
    gui_available = False
    for package, name in gui_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {name}")
            gui_available = True
        except ImportError:
            print(f"⚠️  {name} - Not available")
    
    # Platform-specific tests
    print("\n5. 🖥️  Platform-Specific Features:")
    current_platform = platform.system()
    
    if current_platform == "Windows":
        print("✅ Windows PC detected")
        print("   📱 iPhone: iTunes/3uTools integration")
        print("   🤖 Android: USB file transfer")
        print("   📁 File Explorer: Drag & drop support")
        
        # Test Windows-specific features
        try:
            import os
            if os.path.exists("C:\\"):
                print("   ✅ Windows file system accessible")
        except:
            print("   ⚠️  Windows file system issues")
            
    elif current_platform == "Darwin":  # macOS
        print("✅ macOS detected")
        print("   📱 iPhone: AirDrop/Photos app integration")
        print("   🤖 Android: Android File Transfer")
        print("   📁 Finder: Drag & drop support")
        
        # Test macOS-specific features
        try:
            import os
            if os.path.exists("/Applications"):
                print("   ✅ macOS file system accessible")
        except:
            print("   ⚠️  macOS file system issues")
            
    elif current_platform == "Linux":
        print("✅ Linux detected")
        print("   📱 iPhone: libimobiledevice")
        print("   🤖 Android: MTP/USB mounting")
        print("   📁 File manager: Drag & drop support")
        
        # Test Linux-specific features
        try:
            import os
            if os.path.exists("/home"):
                print("   ✅ Linux file system accessible")
        except:
            print("   ⚠️  Linux file system issues")
    else:
        print(f"⚠️  Unknown platform: {current_platform}")
        print("   Basic functionality expected")
    
    # Test file system access
    print("\n6. 📁 File System Access:")
    try:
        import os
        test_dirs = ['data', 'models', 'config']
        for dir_name in test_dirs:
            if os.path.exists(dir_name):
                print(f"   ✅ {dir_name}/ directory accessible")
            else:
                print(f"   ⚠️  {dir_name}/ directory missing (will be created)")
    except Exception as e:
        print(f"   ❌ File system error: {e}")
    
    # Summary
    print("\n" + "🇮🇱" * 30)
    print("📊 COMPATIBILITY SUMMARY:")
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with: pip install " + " ".join(missing_packages))
    else:
        print("✅ All core packages available")
    
    if ai_available:
        print(f"✅ AI frameworks: {', '.join(ai_available)}")
    else:
        print("⚠️  No AI frameworks detected")
        print("💡 Install with: pip install torch onnxruntime")
    
    if gui_available:
        print("✅ GUI support available")
    else:
        print("⚠️  No GUI support - command line only")
        print("💡 Install with: pip install PyQt5")
    
    print(f"✅ Platform: {current_platform} - Fully supported")
    print("🇮🇱 READY FOR ISRAEL DEFENSE OPERATIONS! 🇮🇱")
    
    return len(missing_packages) == 0

if __name__ == "__main__":
    success = test_pc_compatibility()
    if success:
        print("\n🚀 Run: python main.py --help to get started")
    else:
        print("\n🔧 Install missing packages first")
        sys.exit(1)
