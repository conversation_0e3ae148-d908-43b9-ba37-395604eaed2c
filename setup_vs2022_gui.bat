@echo off
REM 🇮🇱 Israel Defense Forces - Visual Studio 2022 GUI Setup
REM Installs all dependencies and prepares for EXE building

echo 🇮🇱 === ISRAEL DEFENSE FORCES GUI SETUP === 🇮🇱
echo 🎯 Setting up Visual Studio 2022 for GUI app development
echo.

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+ first
    echo 🔗 Download: https://python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python found
python --version

REM Upgrade pip
echo.
echo 🔄 Upgrading pip...
python -m pip install --upgrade pip

REM Install GUI dependencies
echo.
echo 🎨 Installing GUI dependencies...
pip install PyQt5 PyQt5-tools

REM Install AI/ML dependencies
echo.
echo 🤖 Installing AI/ML libraries...
pip install numpy scikit-learn joblib
pip install torch torchvision onnxruntime
pip install Pillow PyYAML

REM Install build tools
echo.
echo 🔨 Installing build tools...
pip install pyinstaller

REM Install optional dependencies
echo.
echo 📱 Installing mobile support...
pip install pillow-heif opencv-python

REM Create necessary directories
echo.
echo 📁 Creating project directories...
if not exist "models" mkdir models
if not exist "data" mkdir data
if not exist "data\input_images" mkdir data\input_images
if not exist "data\train_images" mkdir data\train_images
if not exist "assets" mkdir assets
if not exist "dist" mkdir dist
if not exist "build" mkdir build

REM Create sample config if not exists
if not exist "config\config.yaml" (
    echo 📝 Creating sample configuration...
    echo # Sample configuration > config\config.yaml
    echo paths: >> config\config.yaml
    echo   onnx_model: "models/sample.onnx" >> config\config.yaml
    echo   classifier_pkl: "models/classifier.pkl" >> config\config.yaml
)

REM Test GUI application
echo.
echo 🧪 Testing GUI application...
echo 💡 This will open the GUI for 5 seconds to test...
timeout /t 2 /nobreak >nul
start /wait timeout /t 5 /nobreak >nul & python gui_main.py

echo.
echo ✅ Setup completed successfully!
echo.
echo 🚀 Next steps:
echo   1. Open Visual Studio 2022
echo   2. Open project: 9M83ME.pyproj
echo   3. Press F5 to run GUI application
echo   4. Use build_exe.py to create standalone EXE
echo.
echo 📋 Quick commands:
echo   • Run GUI: python gui_main.py
echo   • Build EXE: python build_exe.py
echo   • Test system: python test_pc_compatibility.py
echo.
echo 🇮🇱 Ready to protect Israel with AI! 🇮🇱
pause
