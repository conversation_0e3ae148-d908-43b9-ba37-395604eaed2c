#!/usr/bin/env python3
"""
Israel Defense Forces Threat Detection System Test
Test the Iranian missile detection capabilities for entire State of Israel
"""

def test_israel_defense():
    """Test the missile detection system for entire Israel defense"""
    print("🇮🇱 Testing Israel Defense Forces - Iranian Missile Detection System")
    print("🏙️  Coverage: Jerusalem • Tel Aviv • Haifa • Beersheba • ALL Israeli cities")
    print("=" * 70)
    
    try:
        # Test 1: Config and Iranian missile database
        print("1. 📋 Testing Iranian missile database...")
        from config import config_loader
        config = config_loader.load_config()
        weapon_classes = config.get('weapon_classes', {})
        
        iranian_categories = [cat for cat in weapon_classes.keys() if 'iranian' in cat.lower()]
        print(f"✅ Found {len(iranian_categories)} Iranian weapon categories")
        
        total_iranian_weapons = 0
        for category in iranian_categories:
            weapons = weapon_classes[category]
            total_iranian_weapons += len(weapons)
            print(f"   {category}: {len(weapons)} weapons")
        
        print(f"✅ Total Iranian weapons in database: {total_iranian_weapons}")
        
        # Test 2: Model flexibility
        print("\n2. 🤖 Testing AI model support...")
        from main import list_all_models
        models = list_all_models()
        if models:
            print(f"✅ Found {len(models)} AI models")
            for model in models:
                print(f"   📁 {model}")
        else:
            print("⚠️  No models found - place ONNX/PyTorch models in models/ folder")
        
        # Test 3: Feature extraction
        print("\n3. 🔍 Testing feature extraction...")
        from classifier.feature_extractor import extract_features
        features = extract_features('data/input_images/test.jpg')
        print(f"✅ Feature extraction working: {features.shape}")
        
        # Test 4: Iranian missile classification
        print("\n4. 🎯 Testing Iranian missile classification...")
        from classifier.classifier import classify_image, get_weapon_info
        result, confidence, _ = classify_image('data/input_images/test.jpg', return_confidence=True)
        weapon_info = get_weapon_info(result)
        israel_threat = weapon_info['israel_threat']

        print(f"✅ Classification working:")
        print(f"   🚀 Detected: {weapon_info['weapon']}")
        print(f"   🏴 Country: {weapon_info['country']}")
        print(f"   🎯 Type: {weapon_info['weapon_type']}")
        print(f"   📏 Range: {weapon_info['range']}")
        print(f"   ⚠️  Threat: {weapon_info['threat_level']}")
        print(f"   🇮🇱 Israel Threat: {israel_threat['threat_level']}")
        print(f"   🏙️  Cities Threatened: {israel_threat['coverage']}")
        print(f"   📊 Confidence: {confidence:.1%}")
        
        # Test 5: Mobile compatibility
        print("\n5. 📱 Testing mobile phone compatibility...")
        mobile_formats = ['.jpg', '.jpeg', '.png', '.heic', '.webp']
        print(f"✅ Supported mobile formats: {', '.join(mobile_formats)}")
        
        print("\n" + "🇮🇱" * 35)
        print("🚀 ISRAEL DEFENSE FORCES SYSTEM OPERATIONAL! 🚀")
        print("🇮🇱" * 35)
        print("\n📱 Ready for mobile phone missile identification")
        print("🚨 Upload photos of suspected Iranian missiles for instant ID")
        print("⚡ Protects ALL Israeli cities from Iranian threats")
        print("🏙️  Jerusalem • Tel Aviv • Haifa • Beersheba • Ramat Gan • ALL ISRAEL")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n🔧 Setup required:")
        print("1. Place AI models in models/ folder")
        print("2. Install dependencies: pip install -r requirements.txt")
        print("3. Create test image: data/input_images/test.jpg")
        return False

if __name__ == "__main__":
    test_israel_defense()
