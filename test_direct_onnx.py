#!/usr/bin/env python3
"""
Test script for direct ONNX classification
Tests the new direct ONNX classifier with your 3N66.onnx model
"""

import sys
import os
from pathlib import Path

def test_direct_onnx_classification():
    """Test the direct ONNX classification functionality"""
    print("🇮🇱 Testing Direct ONNX Classification")
    print("=" * 50)
    
    try:
        from classifier.classifier import classify_image_direct_onnx, get_all_weapon_classes
        print("✅ Direct ONNX classifier imported successfully")
        
        # Test weapon classes loading
        weapon_classes = get_all_weapon_classes()
        print(f"✅ Loaded {len(weapon_classes)} weapon classes")
        print(f"📋 Sample classes: {weapon_classes[:5]}...")
        
        return True
    except Exception as e:
        print(f"❌ Direct ONNX classifier import failed: {e}")
        return False

def test_onnx_model_info():
    """Test ONNX model information"""
    print("\n🔍 Testing ONNX Model Information")
    print("=" * 50)
    
    try:
        import onnxruntime as ort
        from config import config_loader
        
        config = config_loader.load_config()
        onnx_model_path = config['paths']['onnx_model']
        
        print(f"📁 ONNX Model Path: {onnx_model_path}")
        
        if not os.path.exists(onnx_model_path):
            print(f"❌ ONNX model not found at: {onnx_model_path}")
            return False
        
        print(f"✅ ONNX model file exists")
        
        # Load model and get info
        session = ort.InferenceSession(onnx_model_path, providers=['CPUExecutionProvider'])
        
        # Input info
        input_info = session.get_inputs()[0]
        print(f"📥 Input name: {input_info.name}")
        print(f"📥 Input shape: {input_info.shape}")
        print(f"📥 Input type: {input_info.type}")
        
        # Output info
        output_info = session.get_outputs()[0]
        print(f"📤 Output name: {output_info.name}")
        print(f"📤 Output shape: {output_info.shape}")
        print(f"📤 Output type: {output_info.type}")
        
        return True
        
    except Exception as e:
        print(f"❌ ONNX model info test failed: {e}")
        return False

def test_image_classification():
    """Test actual image classification"""
    print("\n🖼️ Testing Image Classification")
    print("=" * 50)
    
    # Look for test images
    image_folders = [
        Path("data/input_images"),
        Path("data/train_images"),
        Path(".")
    ]
    
    test_image = None
    for folder in image_folders:
        if folder.exists():
            for ext in [".jpg", ".jpeg", ".png"]:
                images = list(folder.glob(f"*{ext}"))
                if images:
                    test_image = images[0]
                    break
            if test_image:
                break
    
    if not test_image:
        print("⚠️  No test images found - skipping classification test")
        return True
    
    print(f"📸 Using test image: {test_image}")
    
    try:
        # Test the updated classify_image function
        from classifier.classifier import classify_image
        
        print("🔍 Running classification...")
        result, confidence, class_probs = classify_image(
            str(test_image), 
            return_confidence=True
        )
        
        print(f"✅ Classification successful!")
        print(f"🎯 Predicted weapon: {result}")
        print(f"📊 Confidence: {confidence:.3f}")
        print(f"📈 Top 3 predictions:")
        
        # Sort by probability and show top 3
        sorted_probs = sorted(class_probs.items(), key=lambda x: x[1], reverse=True)
        for i, (weapon, prob) in enumerate(sorted_probs[:3]):
            print(f"   {i+1}. {weapon}: {prob:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Image classification failed: {e}")
        print(f"💡 This might be expected if the ONNX model needs specific preprocessing")
        return False

def test_gui_integration():
    """Test GUI integration"""
    print("\n🖥️ Testing GUI Integration")
    print("=" * 50)
    
    try:
        from gui_main import AnalysisThread
        print("✅ GUI analysis thread imported successfully")
        
        # The GUI should now work with the updated classifier
        print("✅ GUI should now work with direct ONNX classification")
        return True
        
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🇮🇱 Israel Defense Forces - Direct ONNX Classification Test")
    print("=" * 60)
    
    tests = [
        ("Direct ONNX Import", test_direct_onnx_classification),
        ("ONNX Model Info", test_onnx_model_info),
        ("Image Classification", test_image_classification),
        ("GUI Integration", test_gui_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed >= 2:  # At least basic functionality works
        print("🎉 Direct ONNX classification is working!")
        print("✅ Your 3N66.onnx model should now work without PKL classifier")
        print("🚀 Try running the GUI: python gui_main.py")
    else:
        print("⚠️  Some issues detected, but basic ONNX functionality might still work")
        print("💡 Try the GUI anyway - it might work for your specific use case")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    input("Press Enter to exit...")
    sys.exit(0 if success else 1)
