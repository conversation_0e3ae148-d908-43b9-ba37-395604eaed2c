#!/usr/bin/env python3
"""
Test script to verify the refresh functionality works correctly
"""

import sys
from pathlib import Path
import time

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

def test_multiple_analyses():
    """Test that multiple analyses of different images produce different results"""
    print("🔄 TESTING REFRESH FUNCTIONALITY")
    print("=" * 60)
    
    try:
        from classifier.classifier import classify_image
        
        # Test images
        test_images = [
            "data/input_images/Shahed_136_0610152327_texture.png",
            "data/input_images/test.jpg"
        ]
        
        results = []
        
        for i, image_path in enumerate(test_images):
            if not Path(image_path).exists():
                print(f"❌ Image not found: {image_path}")
                continue
                
            print(f"\n🔍 ANALYSIS {i+1}: {Path(image_path).name}")
            print("-" * 40)
            
            # Simulate the refresh process
            print("🔄 Clearing cache...")
            import gc
            gc.collect()
            
            print("🔍 Running fresh analysis...")
            result, confidence, class_probs = classify_image(image_path, return_confidence=True)
            
            # Store result
            analysis_result = {
                'image': Path(image_path).name,
                'weapon': result,
                'confidence': confidence,
                'timestamp': time.strftime("%H:%M:%S")
            }
            results.append(analysis_result)
            
            print(f"✅ Result: {result} ({confidence:.1%})")
            
            # Show top alternatives
            if class_probs:
                sorted_probs = sorted(class_probs.items(), key=lambda x: x[1], reverse=True)
                print("📈 Top alternatives:")
                for j, (weapon, prob) in enumerate(sorted_probs[:3]):
                    if j == 0:
                        continue  # Skip main prediction
                    print(f"   {weapon}: {prob:.1%}")
            
            # Small delay to ensure different timestamps
            time.sleep(1)
        
        print("\n" + "=" * 60)
        print("📊 ANALYSIS SUMMARY")
        print("=" * 60)
        
        # Check if results are different
        if len(results) >= 2:
            different_weapons = len(set(r['weapon'] for r in results)) > 1
            different_confidence = len(set(f"{r['confidence']:.1%}" for r in results)) > 1
            
            print(f"📷 Total analyses: {len(results)}")
            print(f"🎯 Different weapons: {'✅ YES' if different_weapons else '❌ NO'}")
            print(f"📊 Different confidence: {'✅ YES' if different_confidence else '❌ NO'}")
            
            print(f"\n📋 DETAILED RESULTS:")
            for result in results:
                print(f"   [{result['timestamp']}] {result['image']} → {result['weapon']} ({result['confidence']:.1%})")
            
            if different_weapons or different_confidence:
                print(f"\n✅ SUCCESS: Refresh functionality working - different results for different images!")
            else:
                print(f"\n⚠️  WARNING: Same results detected - refresh may not be working properly")
        else:
            print("❌ Not enough results to compare")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_multiple_analyses()
