#!/usr/bin/env python3
"""
🇮🇱 Simple EXE Builder - No Icon Required
Quick build script for Israel Defense Forces Threat Detection
"""

import os
import sys
import subprocess

def build_simple_exe():
    """Build EXE without icon or complex dependencies"""
    print("🇮🇱 Building Israel Defense Forces EXE (Simple Version)...")
    print("=" * 60)
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("✅ PyInstaller found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installed")
    
    # Simple build command (no icon, minimal dependencies)
    build_cmd = [
        "pyinstaller",
        "--onefile",                    # Single EXE file
        "--windowed",                   # No console window
        "--name=IsraelDefenseForces",   # EXE name
        "--clean",                      # Clean build
        "gui_main.py"                   # Main GUI file
    ]
    
    print("🔨 Building EXE with minimal configuration...")
    print(f"Command: {' '.join(build_cmd)}")
    print()
    
    try:
        result = subprocess.run(build_cmd, check=True)
        print("✅ Build successful!")
        print()
        print("📁 Output location: dist/IsraelDefenseForces.exe")
        print("📦 File size: ~50-100MB (minimal dependencies)")
        print()
        print("🚀 Ready to deploy!")
        print("🇮🇱 Protecting Israel from threats! 🇮🇱")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ Build failed!")
        print(f"Error: {e}")
        print()
        print("💡 Troubleshooting:")
        print("1. Ensure PyQt5 is installed: pip install PyQt5")
        print("2. Check that gui_main.py exists")
        print("3. Try running: python gui_main.py (to test)")
        return False

def test_gui():
    """Test if GUI can run before building"""
    print("🧪 Testing GUI application...")
    try:
        # Try importing main modules
        import PyQt5
        print("✅ PyQt5 available")
        
        # Check if main file exists
        if os.path.exists("gui_main.py"):
            print("✅ gui_main.py found")
        else:
            print("❌ gui_main.py not found")
            return False
            
        print("✅ Ready for EXE build")
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Install with: pip install PyQt5")
        return False

if __name__ == "__main__":
    print("🇮🇱 Israel Defense Forces - Simple EXE Builder")
    print()
    
    # Test first
    if test_gui():
        print()
        success = build_simple_exe()
        if success:
            print("\n🎯 Build complete! Test the EXE:")
            print("   dist/IsraelDefenseForces.exe")
        else:
            print("\n❌ Build failed. Check errors above.")
            sys.exit(1)
    else:
        print("\n❌ GUI test failed. Fix dependencies first.")
        sys.exit(1)
