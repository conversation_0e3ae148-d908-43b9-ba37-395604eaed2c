# Security Policy

## 🇮🇱 Israel Defense Forces - Security Guidelines

This project is designed for defense and security applications to protect Israeli citizens. We take security seriously and follow responsible disclosure practices.

## 🛡️ Supported Versions

| Version | Supported          |
| ------- | ------------------ |
| 1.0.x   | ✅ Yes             |
| < 1.0   | ❌ No              |

## 🚨 Reporting a Vulnerability

### For Security Vulnerabilities:
1. **DO NOT** open a public GitHub issue
2. Email security concerns privately to the maintainers
3. Include detailed information about the vulnerability
4. Allow 48-72 hours for initial response

### What to Include:
- Description of the vulnerability
- Steps to reproduce
- Potential impact assessment
- Suggested fix (if known)

## 🔒 Security Measures

### Data Protection:
- No sensitive military data is stored in the repository
- All weapon classifications use publicly available information
- User images are processed locally (no cloud upload)
- No telemetry or data collection

### Code Security:
- Dependencies are regularly updated
- Security scanning via GitHub Actions
- Input validation on all user data
- Safe file handling practices

### AI Model Security:
- ONNX models are validated before use
- No remote model loading
- Sandboxed execution environment
- Input sanitization for image processing

## ⚖️ Legal Compliance

### International Law:
- Complies with international defense regulations
- Uses only publicly available weapon reference data
- No classified or restricted information
- Defensive purpose only

### Export Control:
- Software is designed for defensive applications
- No offensive capabilities included
- Complies with relevant export control laws
- Open source for transparency

## 🇮🇱 Defense Mission

This software exists solely to protect Israeli citizens from Iranian missile threats. All security measures support this defensive mission while maintaining ethical and legal standards.

## 📞 Contact

For security-related questions or concerns, please contact the project maintainers through appropriate channels.

**Remember: This system protects lives. Report security issues responsibly.**
