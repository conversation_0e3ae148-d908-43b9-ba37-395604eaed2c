#!/usr/bin/env python3
"""
Simple test to verify GUI display behavior
"""

import sys
from pathlib import Path

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

def simulate_gui_workflow():
    """Simulate the GUI workflow step by step"""
    print("🖥️ SIMULATING GUI WORKFLOW")
    print("=" * 50)
    
    try:
        # Step 1: Simulate image upload 1
        print("\n📷 STEP 1: Upload Image 1")
        image1_path = "data/input_images/Shahed_136_0610152327_texture.png"
        print(f"  Image path: {image1_path}")
        print("  ✅ Image uploaded - clearing previous results")
        
        # Step 2: Simulate analysis 1
        print("\n🔍 STEP 2: Analyze Image 1")
        from classifier.classifier import classify_image, get_weapon_info
        
        result1, confidence1, class_probs1 = classify_image(image1_path, return_confidence=True)
        weapon_info1 = get_weapon_info(result1)
        
        print(f"  Classification: {result1} ({confidence1:.1%})")
        print(f"  Country: {weapon_info1['country']}")
        print(f"  Type: {weapon_info1['weapon_type']}")
        
        # Simulate GUI display update
        gui_display_1 = f"""
🚨 THREAT DETECTED: {weapon_info1['weapon']}
Country: {weapon_info1['country']}
Type: {weapon_info1['weapon_type']}
Confidence: {confidence1:.1%}
Threat Level: {weapon_info1['threat_level']}
        """
        print(f"  GUI Display 1:\n{gui_display_1}")
        
        # Step 3: Simulate image upload 2 (should clear previous)
        print("\n📷 STEP 3: Upload Image 2")
        image2_path = "data/input_images/test.jpg"
        print(f"  Image path: {image2_path}")
        print("  🔄 Clearing previous results...")
        print("  ✅ Image uploaded - ready for new analysis")
        
        # Step 4: Simulate analysis 2
        print("\n🔍 STEP 4: Analyze Image 2")
        
        # Force fresh analysis (simulate GUI refresh)
        import gc
        gc.collect()
        
        result2, confidence2, class_probs2 = classify_image(image2_path, return_confidence=True)
        weapon_info2 = get_weapon_info(result2)
        
        print(f"  Classification: {result2} ({confidence2:.1%})")
        print(f"  Country: {weapon_info2['country']}")
        print(f"  Type: {weapon_info2['weapon_type']}")
        
        # Simulate GUI display update
        gui_display_2 = f"""
🚨 THREAT DETECTED: {weapon_info2['weapon']}
Country: {weapon_info2['country']}
Type: {weapon_info2['weapon_type']}
Confidence: {confidence2:.1%}
Threat Level: {weapon_info2['threat_level']}
        """
        print(f"  GUI Display 2:\n{gui_display_2}")
        
        # Step 5: Compare results
        print("\n📊 STEP 5: Compare Results")
        different_weapon = result1 != result2
        different_confidence = abs(confidence1 - confidence2) > 0.01
        different_country = weapon_info1['country'] != weapon_info2['country']
        different_type = weapon_info1['weapon_type'] != weapon_info2['weapon_type']
        
        print(f"  Image 1: {Path(image1_path).name} → {result1} ({confidence1:.1%})")
        print(f"  Image 2: {Path(image2_path).name} → {result2} ({confidence2:.1%})")
        print(f"  Different weapon: {'✅ YES' if different_weapon else '❌ NO'}")
        print(f"  Different confidence: {'✅ YES' if different_confidence else '❌ NO'}")
        print(f"  Different country: {'✅ YES' if different_country else '❌ NO'}")
        print(f"  Different type: {'✅ YES' if different_type else '❌ NO'}")
        
        # Overall assessment
        any_difference = different_weapon or different_confidence or different_country or different_type
        print(f"\n🎯 OVERALL RESULT: {'✅ SUCCESS - Different results!' if any_difference else '❌ PROBLEM - Same results!'}")
        
        if not any_difference:
            print("🚨 ISSUE CONFIRMED: GUI would show identical results for different images")
            print("   This suggests the problem is NOT in the GUI display logic")
            print("   The problem is in the classification system itself")
        else:
            print("✅ CLASSIFICATION WORKING: Different images produce different results")
            print("   If GUI shows same results, the problem is in GUI display/threading")
        
        return any_difference
        
    except Exception as e:
        print(f"❌ Error in simulation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_identifications():
    """Test if alternative identifications are working"""
    print("\n🔍 TESTING ALTERNATIVE IDENTIFICATIONS")
    print("=" * 50)
    
    try:
        from classifier.classifier import classify_image
        
        test_images = [
            "data/input_images/Shahed_136_0610152327_texture.png",
            "data/input_images/test.jpg"
        ]
        
        for i, image_path in enumerate(test_images):
            if not Path(image_path).exists():
                continue
                
            print(f"\n📷 Image {i+1}: {Path(image_path).name}")
            
            result, confidence, class_probs = classify_image(image_path, return_confidence=True)
            print(f"  Main: {result} ({confidence:.1%})")
            
            if class_probs:
                sorted_probs = sorted(class_probs.items(), key=lambda x: x[1], reverse=True)
                print(f"  Alternatives:")
                for j, (weapon, prob) in enumerate(sorted_probs[:5]):
                    if j == 0:
                        continue  # Skip main prediction
                    print(f"    {weapon}: {prob:.1%}")
        
        print("✅ Alternative identifications test completed")
        
    except Exception as e:
        print(f"❌ Alternative test error: {e}")

def main():
    """Run the simulation"""
    print("🇮🇱 GUI WORKFLOW SIMULATION")
    print("=" * 60)
    
    # Test main workflow
    success = simulate_gui_workflow()
    
    # Test alternatives
    test_alternative_identifications()
    
    print(f"\n" + "=" * 60)
    if success:
        print("✅ SIMULATION SUCCESSFUL - Classification system working correctly")
        print("   If GUI still shows same results, check GUI display/threading code")
    else:
        print("❌ SIMULATION FAILED - Classification system has issues")
        print("   Need to fix classification logic first")
    print("=" * 60)

if __name__ == "__main__":
    main()
