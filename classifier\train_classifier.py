import os
import numpy as np
import joblib
from PIL import Image
import onnxruntime as ort
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
from pathlib import Path
from config import config_loader
    
# Load configuration
config = config_loader.load_config()
DATA_DIR = Path(config['paths']['train_images'])
MODELS_DIR = Path("models")
MODEL_PATH = MODELS_DIR / "vit_classifier.pkl"
LABELS_PATH = MODELS_DIR / "class_labels.npy"
SUPPORTED_FORMATS = (".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".gif")

def find_onnx_model():
    """Automatically find ONNX models in the models directory."""
    if not MODELS_DIR.exists():
        raise FileNotFoundError(f"Models directory not found: {MODELS_DIR}")
    
    onnx_models = list(MODELS_DIR.glob("*.onnx"))
    if not onnx_models:
        raise FileNotFoundError("No ONNX models found in models directory")
    
    # If multiple models exist, use the most recently modified one
    return str(sorted(onnx_models, key=lambda x: x.stat().st_mtime, reverse=True)[0])

def preprocess_image(image_path, size=(224, 224)):
    img = Image.open(image_path).convert("RGB")
    img = img.resize(size)
    arr = np.array(img).astype(np.float32) / 255.0
    arr = arr.transpose(2, 0, 1)  # Channels first
    arr = np.expand_dims(arr, 0)  # Add batch dimension
    return arr

def extract_features_onnx(image_path, session, input_name, output_name):
    arr = preprocess_image(image_path)
    outputs = session.run([output_name], {input_name: arr})
    return outputs[0].flatten()

def train_classifier(data_dir=None):
    """Train the classifier and return the trained model and class labels."""
    if data_dir is None:
        data_dir = DATA_DIR

    print("[INFO] Finding ONNX model...")
    onnx_path = find_onnx_model()
    print(f"[INFO] Using ONNX model: {onnx_path}")

    print("[INFO] Loading ONNX model...")
    session = ort.InferenceSession(onnx_path, providers=['CPUExecutionProvider'])
    input_name = session.get_inputs()[0].name
    output_name = session.get_outputs()[0].name

    X, y, class_names = [], [], []

    print(f"[INFO] Loading training data from: {data_dir}")

    # Check if data directory exists
    if not data_dir.exists():
        raise FileNotFoundError(f"Training data directory not found: {data_dir}")

    # Get class directories
    class_dirs = [d for d in data_dir.iterdir() if d.is_dir()]
    if not class_dirs:
        raise ValueError(f"No class directories found in: {data_dir}")

    class_names = [d.name for d in class_dirs]
    print(f"[INFO] Found classes: {class_names}")

    # Extract features for each class
    for class_idx, class_dir in enumerate(class_dirs):
        print(f"[INFO] Processing class: {class_dir.name}")

        image_files = []
        for ext in SUPPORTED_FORMATS:
            image_files.extend(class_dir.glob(f"*{ext}"))
            image_files.extend(class_dir.glob(f"*{ext.upper()}"))

        if not image_files:
            print(f"[WARNING] No images found in {class_dir}")
            continue

        for img_path in image_files:
            try:
                features = extract_features_onnx(str(img_path), session, input_name, output_name)
                X.append(features)
                y.append(class_idx)
            except Exception as e:
                print(f"[ERROR] Failed to process {img_path}: {e}")

    if not X:
        raise ValueError("No training data could be loaded")

    X = np.array(X)
    y = np.array(y)

    print(f"[INFO] Training data shape: {X.shape}")
    print(f"[INFO] Labels shape: {y.shape}")

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # Train classifier
    print("[INFO] Training classifier...")
    classifier = LogisticRegression(max_iter=1000, random_state=42)
    classifier.fit(X_train, y_train)

    # Evaluate
    y_pred = classifier.predict(X_test)
    print("\n[INFO] Classification Report:")
    print(classification_report(y_test, y_pred, target_names=class_names))

    # Save model and labels
    os.makedirs(MODELS_DIR, exist_ok=True)
    joblib.dump(classifier, MODEL_PATH)
    np.save(LABELS_PATH, class_names)

    print(f"[INFO] Model saved to: {MODEL_PATH}")
    print(f"[INFO] Labels saved to: {LABELS_PATH}")

    return classifier, class_names

def main():
    """Main training function."""
    try:
        train_classifier()
        print("[SUCCESS] Training completed successfully!")
    except Exception as e:
        print(f"[ERROR] Training failed: {e}")
        raise

if __name__ == "__main__":
    main()

