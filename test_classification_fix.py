#!/usr/bin/env python3
"""
Test script to verify that the classification fix works correctly
and produces different results for different images.
"""

import os
import sys
from pathlib import Path
import numpy as np

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

from classifier.classifier import classify_image, get_weapon_info

def test_classification_differences():
    """Test that different images produce different classification results"""
    
    # Look for test images in the data/input_images folder
    input_folder = Path("data/input_images")
    if not input_folder.exists():
        print("❌ No data/input_images folder found. Please add some test images.")
        print("   Checking if folder exists...")
        print(f"   Current working directory: {Path.cwd()}")
        print(f"   Looking for: {input_folder.absolute()}")
        if Path("data").exists():
            print(f"   data/ folder contents: {list(Path('data').iterdir())}")
        else:
            print("   data/ folder does not exist")
        return
    
    # Find image files
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.heic', '.webp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(input_folder.glob(f"*{ext}"))
        image_files.extend(input_folder.glob(f"*{ext.upper()}"))
    
    if len(image_files) < 2:
        print(f"❌ Need at least 2 images for testing. Found {len(image_files)} images.")
        print("Please add more test images to the input_images folder.")
        return
    
    print(f"🔍 Testing classification differences with {len(image_files)} images...")
    print("=" * 70)
    
    results = []
    
    # Test each image
    for i, image_path in enumerate(image_files[:5]):  # Test up to 5 images
        print(f"\n📷 Testing Image {i+1}: {image_path.name}")
        print("-" * 50)
        
        try:
            # Classify the image
            result, confidence, class_probs = classify_image(
                str(image_path), 
                return_confidence=True
            )
            
            # Get weapon info
            weapon_info = get_weapon_info(result)
            
            # Store results
            results.append({
                'image': image_path.name,
                'weapon': result,
                'confidence': confidence,
                'country': weapon_info['country'],
                'weapon_type': weapon_info['weapon_type'],
                'threat_level': weapon_info['threat_level']
            })
            
            print(f"🚀 Detected: {result}")
            print(f"🏴 Country: {weapon_info['country']}")
            print(f"🎯 Type: {weapon_info['weapon_type']}")
            print(f"⚠️  Threat: {weapon_info['threat_level']}")
            print(f"📊 Confidence: {confidence:.1%}")
            
            # Show top 3 alternative predictions
            if class_probs:
                sorted_probs = sorted(class_probs.items(), key=lambda x: x[1], reverse=True)
                print(f"📈 Top alternatives:")
                for j, (weapon, prob) in enumerate(sorted_probs[:3]):
                    if j == 0:
                        continue  # Skip top prediction (already shown)
                    print(f"   {weapon}: {prob:.1%}")
            
        except Exception as e:
            print(f"❌ Error classifying {image_path.name}: {e}")
            results.append({
                'image': image_path.name,
                'weapon': 'ERROR',
                'confidence': 0.0,
                'country': 'ERROR',
                'weapon_type': 'ERROR',
                'threat_level': 'ERROR'
            })
    
    # Analyze results for differences
    print("\n" + "=" * 70)
    print("🔍 ANALYSIS SUMMARY")
    print("=" * 70)
    
    if len(results) < 2:
        print("❌ Not enough successful classifications to compare.")
        return
    
    # Check if all results are the same (the bug we're trying to fix)
    unique_weapons = set(r['weapon'] for r in results if r['weapon'] != 'ERROR')
    unique_confidences = set(f"{r['confidence']:.1%}" for r in results if r['weapon'] != 'ERROR')
    
    print(f"📊 Total images tested: {len(results)}")
    print(f"🎯 Unique weapons detected: {len(unique_weapons)}")
    print(f"📈 Unique confidence levels: {len(unique_confidences)}")
    
    if len(unique_weapons) == 1 and len(results) > 1:
        print("⚠️  WARNING: All images classified as the same weapon!")
        print("   This suggests the classification fix may not be working properly.")
    elif len(unique_weapons) > 1:
        print("✅ SUCCESS: Different images produced different classifications!")
        print("   The classification fix appears to be working correctly.")
    
    print(f"\n🎯 Detected weapons: {', '.join(unique_weapons)}")
    
    # Show detailed comparison
    print(f"\n📋 DETAILED RESULTS:")
    for result in results:
        if result['weapon'] != 'ERROR':
            print(f"   {result['image']:<25} → {result['weapon']:<15} ({result['confidence']:.1%})")
        else:
            print(f"   {result['image']:<25} → ERROR")

def create_test_embeddings():
    """Create some test embeddings to verify the classification logic"""
    print("\n🧪 Testing embedding classification logic...")
    print("-" * 50)
    
    # Import the function directly
    from classifier.classifier import classify_from_embeddings
    
    # Create different test embeddings
    test_embeddings = [
        np.random.normal(0.2, 0.3, 640),  # High mean, high std
        np.random.normal(0.1, 0.15, 640),  # Medium mean, medium std  
        np.random.normal(0.05, 0.08, 640),  # Low mean, low std
        np.random.uniform(-0.5, 0.5, 640),  # Uniform distribution
        np.random.exponential(0.1, 640),  # Exponential distribution
    ]
    
    embedding_names = [
        "High Normal", "Medium Normal", "Low Normal", 
        "Uniform", "Exponential"
    ]
    
    for i, (embedding, name) in enumerate(zip(test_embeddings, embedding_names)):
        print(f"\n🔬 Test Embedding {i+1}: {name}")
        try:
            weapon, confidence, _ = classify_from_embeddings(embedding)
            print(f"   Result: {weapon} ({confidence:.1%})")
        except Exception as e:
            print(f"   Error: {e}")

if __name__ == "__main__":
    print("🇮🇱 ISRAEL DEFENSE FORCES - Classification Fix Test")
    print("=" * 70)
    
    # Test with actual images
    test_classification_differences()
    
    # Test with synthetic embeddings
    create_test_embeddings()
    
    print("\n" + "=" * 70)
    print("🇮🇱 Test completed! Check results above.")
    print("=" * 70)
