#!/usr/bin/env python3
"""
🇮🇱 Israel Defense Forces - Minimal EXE Builder
Creates a basic standalone Windows executable with minimal dependencies
"""

import os
import sys
import subprocess

def build_minimal():
    """Build minimal standalone EXE for Windows"""
    print("🇮🇱 Building Israel Defense Forces - Minimal Version...")
    print("=" * 60)
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("✅ PyInstaller found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller installed")
        except Exception as e:
            print(f"❌ Failed to install PyInstaller: {e}")
            return False
    
    # Minimal build command - only essential options
    build_cmd = [
        "pyinstaller",
        "--onefile",                    # Single EXE file
        "--windowed",                   # No console window
        "--name=IsraelDefenseForces",   # EXE name
        "--clean",                      # Clean build
        "gui_main.py"                   # Main GUI file
    ]
    
    print("🔨 Building minimal EXE with PyInstaller...")
    print(f"Command: {' '.join(build_cmd)}")
    print()
    
    try:
        # Run without capturing output so user can see progress
        result = subprocess.run(build_cmd, check=True)
        print("\n✅ Minimal build successful!")
        print()
        print("📁 Output location: dist/IsraelDefenseForces.exe")
        print("📦 File size: ~50-100MB (minimal dependencies)")
        print()
        print("⚠️  Note: This minimal build may require:")
        print("   - Manual creation of config/models/data folders")
        print("   - Installation of some dependencies on target PC")
        print()
        print("🚀 Ready for testing!")
        print("🇮🇱 Protecting Israel from threats! 🇮🇱")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Minimal build failed!")
        print(f"Error code: {e.returncode}")
        print("\n💡 Troubleshooting suggestions:")
        print("1. Check if gui_main.py exists in current directory")
        print("2. Try: python gui_main.py (to test if GUI works)")
        print("3. Check Python and PyQt5 installation")
        print("4. Try: pip install PyQt5")
        return False

def test_gui():
    """Test if the GUI can run before building"""
    print("\n🧪 Testing GUI before building...")
    
    if not os.path.exists("gui_main.py"):
        print("❌ gui_main.py not found!")
        return False
    
    try:
        # Try to import the main modules
        import PyQt5
        print("✅ PyQt5 available")
        
        # Test basic import of gui_main
        import importlib.util
        spec = importlib.util.spec_from_file_location("gui_main", "gui_main.py")
        if spec is None:
            print("❌ Could not load gui_main.py")
            return False
        
        print("✅ gui_main.py can be loaded")
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Try: pip install PyQt5")
        return False
    except Exception as e:
        print(f"⚠️  GUI test warning: {e}")
        return True  # Continue anyway

if __name__ == "__main__":
    print("🇮🇱 Israel Defense Forces - Minimal Build Script")
    print("This creates a basic EXE without extra features")
    print()
    
    # Test GUI first
    if test_gui():
        success = build_minimal()
        if success:
            print("\n🎯 Minimal build complete!")
            print("📝 For full-featured build, use: python build_exe.py")
        else:
            print("\n❌ Build failed. Check errors above.")
            sys.exit(1)
    else:
        print("\n❌ GUI test failed. Fix dependencies first.")
        sys.exit(1)
