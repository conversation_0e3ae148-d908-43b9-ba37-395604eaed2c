# Contributing to Israel Defense Forces Threat Detection System

🇮🇱 Thank you for your interest in contributing to the Israel Defense Forces Threat Detection System! This project is critical for protecting Israeli citizens and we welcome contributions from developers worldwide.

## 🎯 How to Contribute

### 1. **Reporting Issues**
- Use GitHub Issues to report bugs or request features
- Include detailed information about your system and the issue
- Provide screenshots or error messages when possible

### 2. **Code Contributions**
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### 3. **Adding New Weapon Classes**
To add support for new Iranian/Russian/Chinese weapons:

1. Add images to `data/train_images/[weapon_name]/`
2. Update `config/config.yaml` with the new weapon class
3. Retrain the model: `python main.py --train`
4. Test the new classification

### 4. **Improving AI Models**
- Contribute better ONNX models for weapon detection
- Improve feature extraction algorithms
- Enhance classification accuracy

## 🔧 Development Setup

```bash
# Clone your fork
git clone https://github.com/your-username/9M83ME.git
cd 9M83ME

# Install dependencies
pip install -r requirements.txt

# Run tests
python test_pc_compatibility.py
python test_redfor.py
```

## 📋 Code Standards

- Follow PEP 8 Python style guidelines
- Add docstrings to all functions
- Include type hints where appropriate
- Write tests for new features
- Update documentation

## 🚨 Security Considerations

- Do not include actual classified weapon data
- Use only publicly available reference images
- Ensure all contributions comply with international law
- Report security vulnerabilities privately

## 🇮🇱 Mission Statement

This project exists to protect Israeli citizens from Iranian missile threats. All contributions should align with this defensive mission and comply with applicable laws.

## 📞 Contact

For questions about contributing, please open a GitHub issue or contact the maintainers.

Thank you for helping protect Israel! 🇮🇱
