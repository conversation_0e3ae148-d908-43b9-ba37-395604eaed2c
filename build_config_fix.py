#!/usr/bin/env python3
"""
Quick build script to test config.yaml fix
"""

import os
import sys
import subprocess

def build_with_config_fix():
    """Build EXE with config fix included"""
    print("🇮🇱 Building Israel Defense Forces with Config Fix...")
    print("=" * 60)
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("✅ PyInstaller found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installed")
    
    # Build command with config directory included
    build_cmd = [
        "pyinstaller",
        "--onefile",                    # Single EXE file
        "--windowed",                   # No console window
        "--name=IsraelDefenseForces_ConfigFix",   # EXE name
        "--clean",                      # Clean build
        "--add-data=config;config",     # Include config directory
        "gui_main.py"                   # Main GUI file
    ]
    
    # Add icon if available
    if os.path.exists("assets/icon.ico"):
        build_cmd.insert(-1, "--icon=assets/icon.ico")
        print("✅ Using icon: assets/icon.ico")
    
    print("🔨 Building EXE with config fix...")
    print(f"Command: {' '.join(build_cmd)}")
    print()
    
    try:
        result = subprocess.run(build_cmd, check=True, capture_output=True, text=True)
        print("✅ Build successful!")
        print()
        print("📁 Output location: dist/IsraelDefenseForces_ConfigFix.exe")
        print("🔧 Config fix included - should resolve config.yaml error")
        print()
        print("🧪 Test the EXE to verify config loading works!")
        
        # Check file size
        exe_path = "dist/IsraelDefenseForces_ConfigFix.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📦 Size: {size_mb:.1f} MB")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ Build failed!")
        print(f"Error: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def test_config_in_exe():
    """Test if the built EXE can load config"""
    exe_path = "dist/IsraelDefenseForces_ConfigFix.exe"
    
    if not os.path.exists(exe_path):
        print("❌ EXE not found, cannot test")
        return False
    
    print("\n🧪 Testing config loading in built EXE...")
    print("Note: This will launch the GUI briefly to test config loading")
    
    # We can't easily test the EXE automatically, so just inform the user
    print("✅ EXE built successfully with config fix")
    print("🔍 Please test manually by running the EXE")
    print("📝 The config.yaml error should be resolved")
    
    return True

def main():
    """Main build function"""
    print("🇮🇱 Israel Defense Forces - Config Fix Builder")
    print("Building EXE with improved config.yaml loading")
    print()
    
    # Build the EXE
    if build_with_config_fix():
        test_config_in_exe()
        print("\n🎯 Build complete!")
        print("✅ Config fix has been applied")
        print("🚀 Ready to test the new EXE!")
        return 0
    else:
        print("\n❌ Build failed. Check errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
