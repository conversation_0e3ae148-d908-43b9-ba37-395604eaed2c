# 💻 PC Installation Guide - Israel Defense Forces Threat Detection

## 🖥️ **FULL PC COMPATIBILITY**

This system works on **ALL PC platforms**:
- ✅ **Windows 10/11** (Recommended)
- ✅ **macOS** (Intel & Apple Silicon)
- ✅ **Linux** (Ubuntu, CentOS, etc.)

## 🚀 **Quick Installation**

### **Option 1: Automatic Installation (Recommended)**
```bash
# Clone or download the project
cd 9M83ME

# Install all dependencies
pip install -r requirements.txt

# Test the system
python main.py --help
```

### **Option 2: Manual Installation**
```bash
# Core AI libraries
pip install numpy scikit-learn joblib

# Image processing
pip install Pillow opencv-python

# Deep learning (choose what you need)
pip install torch torchvision onnxruntime

# Configuration
pip install PyYAML

# GUI (optional)
pip install PyQt5

# iPhone support
pip install pillow-heif
```

## 🖥️ **Platform-Specific Instructions**

### **Windows 10/11** 🪟
```powershell
# Install Python 3.8+ from python.org
# Open PowerShell as Administrator

# Install dependencies
pip install -r requirements.txt

# For better Windows integration (optional)
pip install pywin32

# Run the system
python main.py --select-model
```

### **macOS** 🍎
```bash
# Install Python via Homebrew (recommended)
brew install python

# Install dependencies
pip3 install -r requirements.txt

# For better macOS integration (optional)
pip3 install pyobjc

# Run the system
python3 main.py --select-model
```

### **Linux (Ubuntu/Debian)** 🐧
```bash
# Install Python and pip
sudo apt update
sudo apt install python3 python3-pip

# Install system dependencies
sudo apt install python3-tk  # For GUI

# Install Python dependencies
pip3 install -r requirements.txt

# Run the system
python3 main.py --select-model
```

## 📱 **Mobile Phone Integration on PC**

### **Windows PC + iPhone**
1. **iTunes/3uTools**: Connect iPhone via USB
2. **iCloud Photos**: Sync to Windows Photos app
3. **AirDrop Alternative**: Use apps like SHAREit
4. **File Explorer**: Access iPhone photos directly

### **Windows PC + Android**
1. **USB Connection**: Enable file transfer mode
2. **Google Photos**: Sync to PC
3. **Samsung DeX**: Direct integration
4. **File Explorer**: Access phone storage

### **Mac + iPhone** (Easiest)
1. **AirDrop**: Instant photo transfer
2. **Photos App**: Automatic sync
3. **Image Capture**: Direct import
4. **iCloud**: Seamless integration

### **Mac + Android**
1. **Android File Transfer**: Official Google app
2. **Google Photos**: Web/app sync
3. **USB Connection**: Direct file access

## 🎯 **PC-Optimized Usage**

### **Desktop Workflow**
```bash
# 1. Setup (one time)
python main.py --select-model

# 2. Drag & drop photos to input folder
# Copy photos to: data/input_images/

# 3. Batch analyze all photos
python main.py --batch-classify

# 4. Or analyze single photo
python main.py --infer path/to/missile_photo.jpg
```

### **GUI Mode (Visual Interface)**
```bash
# Launch visual interface
python main.py --gui

# Features:
# - Drag & drop images
# - Real-time classification
# - Visual threat assessment
# - Export results
```

## 🔧 **Troubleshooting PC Issues**

### **Common Windows Issues**
```powershell
# Python not found
# Solution: Add Python to PATH or reinstall from python.org

# Permission errors
# Solution: Run PowerShell as Administrator

# PyQt5 installation fails
# Solution: Try PyQt6 instead
pip uninstall PyQt5
pip install PyQt6
```

### **Common macOS Issues**
```bash
# Command not found: python
# Solution: Use python3
alias python=python3

# Permission denied
# Solution: Use --user flag
pip3 install --user -r requirements.txt

# M1/M2 Mac compatibility
# Solution: Use conda for better ARM support
conda install pytorch torchvision -c pytorch
```

### **Common Linux Issues**
```bash
# Missing GUI libraries
sudo apt install python3-tk python3-dev

# CUDA support (for GPU acceleration)
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# Permission issues
sudo chown -R $USER:$USER ~/.local/
```

## ⚡ **Performance Optimization**

### **GPU Acceleration** (Optional)
```bash
# NVIDIA GPU (Windows/Linux)
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# AMD GPU (Linux)
pip install torch torchvision --index-url https://download.pytorch.org/whl/rocm5.4.2

# Apple Silicon (macOS)
# PyTorch automatically uses Metal Performance Shaders
```

### **CPU Optimization**
```bash
# Multi-threading support
export OMP_NUM_THREADS=4

# Intel MKL optimization (Intel CPUs)
pip install mkl

# Memory optimization for large images
# Edit config.yaml: reduce image_size if needed
```

## 📊 **System Requirements**

### **Minimum Requirements**
- **CPU**: Intel i3 / AMD Ryzen 3 or equivalent
- **RAM**: 4GB (8GB recommended)
- **Storage**: 2GB free space
- **Python**: 3.8 or newer
- **Internet**: For initial model download

### **Recommended Requirements**
- **CPU**: Intel i5 / AMD Ryzen 5 or better
- **RAM**: 8GB+ (16GB for large models)
- **GPU**: NVIDIA GTX 1060+ or equivalent (optional)
- **Storage**: 10GB+ for models and data
- **Python**: 3.9 or 3.10

## ✅ **Verification**

Test your installation:
```bash
# Quick test
python main.py --help

# Full system test
python tel_aviv_defense_test.py

# GUI test (if installed)
python main.py --gui
```

Your PC is now ready to protect Israel from Iranian missile threats! 🇮🇱
