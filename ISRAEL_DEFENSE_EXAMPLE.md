# 🇮🇱 Israel Defense Forces - Threat Detection Example

## Sample Output for Iranian Fateh-110 Missile Detection

```
🔍 Analyzing image for threats to Israel: iranian_missile.jpg

🇮🇱======================================================================🇮🇱
🎯 ISRAEL DEFENSE FORCES - MISSILE IDENTIFICATION SYSTEM
🇮🇱======================================================================🇮🇱
📷 Image: iranian_missile.jpg
🚀 Detected Weapon: Fateh-110
🏴 Country/Origin: IRANIAN
🎯 Weapon Type: Ballistic Missile
📏 Range: 300km (300km)
🎯 Precision: High
⚠️  Threat Level: HIGH
📊 AI Confidence: 87.3%

🇮🇱 ISRAELI TERRITORY THREAT ASSESSMENT:
🎯 Threat Coverage: CENTRAL ISRAEL
🏙️  Cities at Risk: 8 cities threatened

🏘️  Threatened Cities:
   • Sderot
   • Ashkelon
   • Ashdod
   • Tel Aviv
   • Ramat Gan
   • Petah Tikva
   • Holon
   • Bnei Brak

🏭 Strategic Sites at Risk:
   • Ben Gurion Airport
   • Ashdod Port

⚠️  CENTRAL ISRAEL THREAT: Can reach Tel Aviv, Jerusalem, Haifa

📈 Alternative Identifications:
   Fateh-313 (Ballistic Missile): 8.2%
   Qiam-1 (Ballistic Missile): 4.5%
   Zolfaghar (Ballistic Missile): 2.1%

🇮🇱======================================================================🇮🇱
📞 REPORT TO IDF: *3456 or local emergency services
🚨 SHARE WITH SECURITY FORCES if confirmed threat
🇮🇱======================================================================🇮🇱
```

## Sample Output for Long-Range Iranian Missile (Khorramshahr)

```
🔍 Analyzing image for threats to Israel: long_range_missile.jpg

🇮🇱======================================================================🇮🇱
🎯 ISRAEL DEFENSE FORCES - MISSILE IDENTIFICATION SYSTEM
🇮🇱======================================================================🇮🇱
📷 Image: long_range_missile.jpg
🚀 Detected Weapon: Khorramshahr
🏴 Country/Origin: IRANIAN
🎯 Weapon Type: MIRV Ballistic Missile
📏 Range: 2000km (2000km)
🎯 Precision: High
⚠️  Threat Level: EXTREME
📊 AI Confidence: 92.1%

🇮🇱 ISRAELI TERRITORY THREAT ASSESSMENT:
🎯 Threat Coverage: ENTIRE ISRAEL
🏙️  Cities at Risk: 15 cities threatened

🏘️  Threatened Cities:
   • Sderot
   • Ashkelon
   • Ashdod
   • Tel Aviv
   • Ramat Gan
   • Petah Tikva
   • Holon
   • Bnei Brak
   • Jerusalem
   • Haifa
   • ... and 5 more cities

🏭 Strategic Sites at Risk:
   • Ben Gurion Airport
   • Haifa Port
   • Ashdod Port
   • Dimona Nuclear Facility
   • IDF Bases (Central)

🚨 HIGH PRIORITY ALERT 🚨
⚡ Khorramshahr detected - immediate IDF assessment required

🚨 NATIONAL THREAT: Can reach entire State of Israel including Eilat
💥 MIRV THREAT: Multiple warheads, extremely dangerous

🇮🇱======================================================================🇮🇱
📞 REPORT TO IDF: *3456 or local emergency services
🚨 SHARE WITH SECURITY FORCES if confirmed threat
🇮🇱======================================================================🇮🇱
```

## Israeli Cities Coverage Map

### Range Categories:
- **5-40km**: Gaza Border Area (Sderot, Ashkelon)
- **40-70km**: Southern Israel (Ashdod, Ashkelon)
- **70-120km**: Central Israel (Tel Aviv, Ramat Gan, Petah Tikva, Jerusalem)
- **120-200km**: Northern Israel (Haifa, Netanya)
- **200-350km**: Most of Israel (Beersheba, Dimona)
- **350km+**: Entire Israel (including Eilat)

### Strategic Sites Protected:
- 🛫 **Ben Gurion Airport** (Critical)
- ⚓ **Haifa Port** (Critical)
- ⚓ **Ashdod Port** (High)
- ⚛️ **Dimona Nuclear Facility** (Critical)
- 🏭 **IDF Military Bases** (Critical)

## Mobile Phone Integration

### Supported Formats:
- 📱 **iPhone**: HEIC, JPG, PNG
- 🤖 **Android**: JPG, PNG, WebP
- 💻 **Computer**: All standard image formats

### Quick Commands:
```bash
# Interactive photo selection
python main.py --infer-interactive

# Process phone camera folder
python main.py --mobile-batch "/Users/<USER>/Pictures/iPhone"

# Direct analysis
python main.py --infer suspicious_missile.jpg
```

This system provides comprehensive threat assessment for the entire State of Israel, helping protect all Israeli citizens from Iranian missile threats.
