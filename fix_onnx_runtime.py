#!/usr/bin/env python3
"""
ONNX Runtime DLL Fix Script for Windows
Fixes the common DLL initialization error in ONNX Runtime on Windows
"""

import subprocess
import sys
import os
import platform
import urllib.request
import tempfile
from pathlib import Path

def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} failed")
            if result.stderr.strip():
                print(f"Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ {description} failed with exception: {e}")
        return False

def check_vcredist():
    """Check if Visual C++ Redistributable is installed"""
    print("\n🔍 Checking Visual C++ Redistributable...")
    
    # Check registry for VC++ Redistributable
    try:
        import winreg
        key_path = r"SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64"
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path) as key:
            version = winreg.QueryValueEx(key, "Version")[0]
            print(f"✅ Visual C++ Redistributable found: {version}")
            return True
    except:
        pass
    
    # Alternative check
    try:
        result = subprocess.run(
            'wmic product where "name like \'%Visual C++%\'" get name,version',
            shell=True, capture_output=True, text=True
        )
        if "Visual C++" in result.stdout:
            print("✅ Visual C++ Redistributable found")
            return True
    except:
        pass
    
    print("❌ Visual C++ Redistributable not found or not accessible")
    return False

def download_vcredist():
    """Download and suggest installation of VC++ Redistributable"""
    print("\n📥 Visual C++ Redistributable Download Info:")
    print("🔗 Download from: https://aka.ms/vs/17/release/vc_redist.x64.exe")
    print("💡 This is required for ONNX Runtime to work properly on Windows")
    
    response = input("\nWould you like to open the download page? (y/n): ")
    if response.lower() == 'y':
        try:
            import webbrowser
            webbrowser.open("https://aka.ms/vs/17/release/vc_redist.x64.exe")
            print("✅ Download page opened in browser")
        except:
            print("❌ Could not open browser automatically")

def fix_onnx_runtime():
    """Main function to fix ONNX Runtime DLL issues"""
    print("🇮🇱 Israel Defense Forces - ONNX Runtime DLL Fix")
    print("=" * 60)
    
    # Check if we're on Windows
    if platform.system() != "Windows":
        print("❌ This fix is specifically for Windows systems")
        return False
    
    print(f"🖥️  System: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version}")
    
    # Step 1: Check current ONNX Runtime
    print("\n📦 Checking current ONNX Runtime installation...")
    try:
        import onnxruntime
        current_version = onnxruntime.__version__
        print(f"Current ONNX Runtime version: {current_version}")
        
        # Test if it works
        try:
            providers = onnxruntime.get_available_providers()
            print(f"Available providers: {providers}")
        except Exception as e:
            print(f"❌ ONNX Runtime test failed: {e}")
    except ImportError:
        print("❌ ONNX Runtime not installed")
        current_version = None
    
    # Step 2: Check Visual C++ Redistributable
    vcredist_ok = check_vcredist()
    if not vcredist_ok:
        download_vcredist()
        print("\n⚠️  Please install Visual C++ Redistributable and run this script again")
        return False
    
    # Step 3: Uninstall current ONNX Runtime
    if current_version:
        success = run_command(
            "pip uninstall onnxruntime -y",
            "Uninstalling current ONNX Runtime"
        )
        if not success:
            print("⚠️  Failed to uninstall current ONNX Runtime, continuing anyway...")
    
    # Step 4: Install compatible version
    # Try different versions in order of preference
    versions_to_try = ["1.20.0", "1.20.1", "1.21.0"]
    success = False

    for version in versions_to_try:
        print(f"\n🔧 Trying ONNX Runtime {version}...")
        success = run_command(
            f"pip install onnxruntime=={version}",
            f"Installing ONNX Runtime {version} (Windows compatible)"
        )
        if success:
            break

    if not success:
        print("❌ Failed to install any compatible ONNX Runtime version")
        print("💡 Trying latest available version...")
        success = run_command(
            "pip install onnxruntime",
            "Installing latest ONNX Runtime"
        )
    
    if not success:
        print("❌ Failed to install ONNX Runtime 1.16.3")
        return False
    
    # Step 5: Test the installation
    print("\n🧪 Testing ONNX Runtime installation...")
    try:
        import importlib
        if 'onnxruntime' in sys.modules:
            importlib.reload(sys.modules['onnxruntime'])
        
        import onnxruntime as ort
        print(f"✅ ONNX Runtime {ort.__version__} imported successfully")
        
        # Test session creation
        providers = ort.get_available_providers()
        print(f"✅ Available providers: {providers}")
        
        # Test with a dummy model (if we can create one)
        try:
            session_options = ort.SessionOptions()
            session_options.enable_cpu_mem_arena = False
            session_options.enable_mem_pattern = False
            print("✅ Session options configured successfully")
        except Exception as e:
            print(f"⚠️  Session options test failed: {e}")
        
        print("\n🎉 ONNX Runtime fix completed successfully!")
        print("💡 You can now run your threat detection system")
        return True
        
    except Exception as e:
        print(f"❌ ONNX Runtime test failed: {e}")
        print("\n💡 Additional troubleshooting:")
        print("1. Restart your computer")
        print("2. Check antivirus settings")
        print("3. Try running as administrator")
        return False

def main():
    """Main entry point"""
    try:
        success = fix_onnx_runtime()
        if success:
            print("\n✅ Fix completed successfully!")
            print("🚀 You can now run: python gui_main.py")
        else:
            print("\n❌ Fix failed. Please check the error messages above.")
            print("🆘 For support, check the VS2022_TROUBLESHOOTING.md file")
        
        input("\nPress Enter to exit...")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Fix cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
