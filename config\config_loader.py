import yaml
import os
import sys
from pathlib import Path

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON>yInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
        print(f"[CONFIG] Running from PyInstaller, base path: {base_path}")
    except AttributeError:
        # Running in development mode
        base_path = os.path.abspath(".")
        print(f"[CONFIG] Running in development mode, base path: {base_path}")

    full_path = os.path.join(base_path, relative_path)
    print(f"[CONFIG] Looking for resource: {full_path}")
    return full_path

def load_config(path='config/config.yaml'):
    """Load configuration file, handling both development and .exe environments"""

    # Try multiple possible locations for config file
    possible_paths = [
        path,  # Original path (for development)
        get_resource_path(path),  # PyInstaller bundled path
        get_resource_path('config.yaml'),  # Direct config.yaml in bundle
        os.path.join(os.path.dirname(__file__), '..', path),  # Relative to this file
        os.path.join(os.getcwd(), path),  # Current working directory
    ]

    config_path = None
    for test_path in possible_paths:
        print(f"[CONFIG] Trying config path: {test_path}")
        if os.path.exists(test_path):
            config_path = test_path
            print(f"[CONFIG] ✅ Found config at: {config_path}")
            break
        else:
            print(f"[CONFIG] ❌ Not found: {test_path}")

    if not config_path:
        # Create a minimal default config if none found
        print("[CONFIG] ⚠️  No config.yaml found, creating minimal default config")
        return create_default_config()

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            print(f"[CONFIG] ✅ Successfully loaded config from: {config_path}")
            return config
    except Exception as e:
        print(f"[CONFIG] ❌ Failed to load config from {config_path}: {e}")
        return create_default_config()

def create_default_config():
    """Create a minimal default configuration"""
    return {
        'paths': {
            'assets_folder': 'assets/',
            'classifier_pkl': 'models/redfor_classifier.pkl',
            'input_images': 'data/input_images/',
            'onnx_model': 'models/3N66.onnx',
            'render_output': 'data/generated_views/',
            'train_images': 'data/train_images/'
        },
        'weapon_classes': {
            'iranian_drones_loitering': [
                'Shahed-136', 'Shahed-131', 'Geran-1', 'Geran-2'
            ],
            'iranian_ballistic_missiles': [
                'Fateh-110', 'Fateh-313', 'Zolfaghar', 'Dezful'
            ]
        },
        'train': {
            'model_type': 'LogisticRegression',
            'random_seed': 42,
            'test_split': 0.2,
            'confidence_threshold': 0.7
        }
    }