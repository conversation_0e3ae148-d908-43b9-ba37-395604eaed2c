#!/usr/bin/env python3
"""
🇮🇱 Israel Defense Forces - Robust EXE Builder
Tries multiple build strategies to ensure success
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check and install required dependencies"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        ("PyInstaller", "pyinstaller"),
        ("PyQt5", "PyQt5"),
        ("Pillow", "Pillow"),
    ]
    
    missing = []
    for display_name, package_name in required_packages:
        try:
            __import__(display_name.lower().replace('-', '_'))
            print(f"✅ {display_name} found")
        except ImportError:
            print(f"❌ {display_name} missing")
            missing.append(package_name)
    
    if missing:
        print(f"📦 Installing missing packages: {', '.join(missing)}")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing)
            print("✅ All dependencies installed")
        except Exception as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    return True

def create_directories():
    """Create necessary directories"""
    dirs = ["assets", "config", "models", "data", "dist"]
    for d in dirs:
        os.makedirs(d, exist_ok=True)
        print(f"📁 Directory ready: {d}")

def build_strategy_1():
    """Strategy 1: Full build with all features"""
    print("\n🚀 Strategy 1: Full build with all features")
    
    # Create icon first
    try:
        from create_icon import create_icon
        icon_path = create_icon()
    except Exception as e:
        print(f"⚠️  Icon creation failed: {e}")
        icon_path = None
    
    build_cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=IsraelDefenseForces",
        "--clean",
    ]
    
    # Add icon if available
    if icon_path and os.path.exists(icon_path):
        build_cmd.append(f"--icon={icon_path}")
    
    # Add data directories
    for src, dst in [("config", "config"), ("models", "models"), ("data", "data")]:
        if os.path.exists(src):
            build_cmd.append(f"--add-data={src};{dst}")
    
    # Add hidden imports
    imports = ["PyQt5", "sklearn", "torch", "onnxruntime", "PIL", "yaml", "numpy"]
    for imp in imports:
        build_cmd.append(f"--hidden-import={imp}")
    
    build_cmd.append("gui_main.py")
    
    try:
        subprocess.run(build_cmd, check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def build_strategy_2():
    """Strategy 2: Build without icon"""
    print("\n🚀 Strategy 2: Build without icon")
    
    build_cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=IsraelDefenseForces",
        "--clean",
        "--hidden-import=PyQt5",
        "gui_main.py"
    ]
    
    try:
        subprocess.run(build_cmd, check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def build_strategy_3():
    """Strategy 3: Minimal build"""
    print("\n🚀 Strategy 3: Minimal build")
    
    build_cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=IsraelDefenseForces",
        "gui_main.py"
    ]
    
    try:
        subprocess.run(build_cmd, check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def verify_build():
    """Verify the build was successful"""
    exe_path = "dist/IsraelDefenseForces.exe"
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)
        print(f"✅ Build successful!")
        print(f"📁 Location: {exe_path}")
        print(f"📦 Size: {size_mb:.1f} MB")
        return True
    else:
        print("❌ Build failed - EXE not found")
        return False

def main():
    """Main build process"""
    print("🇮🇱 Israel Defense Forces - Robust Build System")
    print("=" * 60)
    
    # Check if main file exists
    if not os.path.exists("gui_main.py"):
        print("❌ gui_main.py not found!")
        print("💡 Make sure you're in the correct directory")
        return False
    
    # Check dependencies
    if not check_dependencies():
        return False
    
    # Create directories
    create_directories()
    
    # Try build strategies in order
    strategies = [build_strategy_1, build_strategy_2, build_strategy_3]
    
    for i, strategy in enumerate(strategies, 1):
        print(f"\n{'='*60}")
        try:
            if strategy():
                if verify_build():
                    print(f"\n🎯 Success with Strategy {i}!")
                    print("🇮🇱 Protecting Israel from threats! 🇮🇱")
                    return True
        except Exception as e:
            print(f"❌ Strategy {i} failed: {e}")
            continue
    
    print("\n❌ All build strategies failed!")
    print("💡 Try running: python build_minimal.py")
    return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
