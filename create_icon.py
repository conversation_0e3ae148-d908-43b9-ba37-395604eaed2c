#!/usr/bin/env python3
"""
Create a generic icon for Military Threat Detection System
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_icon():
    """Create a generic, professional icon for the application"""
    print("Creating application icon...")

    try:
        # Check if PIL is available
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        print("PIL (Pillow) not found. Installing...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
        from PIL import Image, ImageDraw, ImageFont

    # Create assets directory
    os.makedirs("assets", exist_ok=True)

    # Create a 256x256 icon
    size = 256
    img = Image.new('RGBA', (size, size), (240, 240, 240, 255))  # Light gray background
    draw = ImageDraw.Draw(img)

    # Professional, boring colors
    dark_gray = (44, 62, 80)     # Dark blue-gray
    medium_gray = (52, 73, 94)   # Medium gray
    light_gray = (149, 165, 166) # Light gray
    white = (255, 255, 255)

    # Draw outer border (boring rectangle)
    border_width = 8
    draw.rectangle([border_width, border_width, size-border_width, size-border_width],
                  outline=dark_gray, width=border_width)

    # Inner rectangle
    inner_margin = 40
    draw.rectangle([inner_margin, inner_margin, size-inner_margin, size-inner_margin],
                  fill=white, outline=medium_gray, width=4)

    # Simple crosshair/target symbol in center (generic military symbol)
    center = size // 2
    crosshair_size = 60
    line_width = 6

    # Horizontal line
    draw.line([center - crosshair_size, center, center + crosshair_size, center],
              fill=dark_gray, width=line_width)
    # Vertical line
    draw.line([center, center - crosshair_size, center, center + crosshair_size],
              fill=dark_gray, width=line_width)

    # Small circle in center
    circle_radius = 12
    draw.ellipse([center - circle_radius, center - circle_radius,
                  center + circle_radius, center + circle_radius],
                 outline=dark_gray, width=4)

    # Add boring text overlay
    try:
        # Try to use a standard font
        font = ImageFont.truetype("arial.ttf", 16)
        small_font = ImageFont.truetype("arial.ttf", 12)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
        small_font = ImageFont.load_default()

    # Add generic military abbreviation
    text = "MTS"  # Military Threat System
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_x = (size - text_width) // 2
    text_y = size - 35

    draw.text((text_x, text_y), text, fill=dark_gray, font=font)

    # Add version number to make it look more boring/technical
    version_text = "v1.0"
    bbox = draw.textbbox((0, 0), version_text, font=small_font)
    version_width = bbox[2] - bbox[0]
    version_x = (size - version_width) // 2
    version_y = size - 18

    draw.text((version_x, version_y), version_text, fill=light_gray, font=small_font)

    # Save as ICO file
    icon_path = "assets/icon.ico"

    # Create multiple sizes for ICO file
    sizes = [16, 32, 48, 64, 128, 256]
    images = []

    for ico_size in sizes:
        resized = img.resize((ico_size, ico_size), Image.Resampling.LANCZOS)
        images.append(resized)

    # Save as ICO
    try:
        img.save(icon_path, format='ICO', sizes=[(s, s) for s in sizes])
        print(f"Icon created: {icon_path}")
        print("Generic crosshair design with neutral colors")
        return icon_path
    except Exception as e:
        print(f"Failed to save icon: {e}")
        # Try saving as PNG instead
        png_path = "assets/icon.png"
        try:
            img.save(png_path, format='PNG')
            print(f"Icon saved as PNG: {png_path}")
            print("Note: PNG format, not ICO")
            return png_path
        except Exception as e2:
            print(f"Failed to save PNG: {e2}")
            return None

if __name__ == "__main__":
    create_icon()
