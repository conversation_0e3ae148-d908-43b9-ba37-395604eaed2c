#!/usr/bin/env python3
"""
Test script for 9M83ME Anti-REDFOR Detection System
"""

def test_classification():
    """Test the REDFOR weapon classification"""
    print("=== Testing REDFOR Detection System ===")
    
    try:
        # Test config loading
        from config import config_loader
        config = config_loader.load_config()
        print("✓ Config loaded successfully")
        
        # Test weapon classes
        weapon_classes = config.get('weapon_classes', {})
        print(f"✓ Weapon classes loaded: {len(weapon_classes)} countries")
        
        for country, weapons in weapon_classes.items():
            print(f"  {country.upper()}: {', '.join(weapons)}")
        
        # Test feature extraction
        from classifier.feature_extractor import extract_features
        features = extract_features('data/input_images/test.jpg')
        print(f"✓ Feature extraction working: shape {features.shape}")
        
        # Test classification
        from classifier.classifier import classify_image, get_weapon_info
        result, confidence, class_probs = classify_image('data/input_images/test.jpg', return_confidence=True)
        weapon_info = get_weapon_info(result)
        
        print(f"✓ Classification working:")
        print(f"  Detected: {weapon_info['weapon']}")
        print(f"  Country: {weapon_info['country']}")
        print(f"  Confidence: {confidence:.2%}")
        
        print("\n🎯 REDFOR Detection System is operational!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_classification()
