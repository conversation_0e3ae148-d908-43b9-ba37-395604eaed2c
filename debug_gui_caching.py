#!/usr/bin/env python3
"""
Comprehensive diagnostic to identify GUI caching and display issues
"""

import sys
from pathlib import Path
import time
import hashlib

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

def test_classification_caching():
    """Test if classification system is caching results"""
    print("🔍 TESTING CLASSIFICATION CACHING")
    print("=" * 60)
    
    try:
        from classifier.classifier import classify_image_direct_onnx
        import onnxruntime as ort
        from PIL import Image
        import torchvision.transforms as transforms
        from config import config_loader
        import numpy as np
        
        # Test images
        test_images = [
            "data/input_images/Shahed_136_0610152327_texture.png",
            "data/input_images/test.jpg"
        ]
        
        for i, image_path in enumerate(test_images):
            if not Path(image_path).exists():
                print(f"❌ Image not found: {image_path}")
                continue
                
            print(f"\n📷 IMAGE {i+1}: {Path(image_path).name}")
            print("-" * 40)
            
            # Test 1: Check if image content is actually different
            with open(image_path, 'rb') as f:
                image_hash = hashlib.md5(f.read()).hexdigest()[:8]
            print(f"📋 Image hash: {image_hash}")
            
            # Test 2: Get raw embeddings multiple times
            config = config_loader.load_config()
            onnx_model_path = config['paths']['onnx_model']
            
            session_options = ort.SessionOptions()
            session_options.enable_cpu_mem_arena = False
            session_options.enable_mem_pattern = False
            
            session = ort.InferenceSession(
                onnx_model_path,
                providers=['CPUExecutionProvider'],
                sess_options=session_options
            )
            
            # Preprocess image
            img = Image.open(image_path).convert("RGB")
            transform = transforms.Compose([
                transforms.Resize((240, 240)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
            ])
            img_tensor = transform(img).unsqueeze(0).numpy()
            
            # Get embeddings multiple times to check for consistency
            embeddings_list = []
            for run in range(3):
                input_name = session.get_inputs()[0].name
                outputs = session.run(None, {input_name: img_tensor})
                embedding = outputs[0][0]  # Shape: [640]
                embeddings_list.append(embedding)
                
                # Calculate key stats
                mean = np.mean(embedding)
                std = np.std(embedding)
                sum_val = np.sum(embedding)
                
                print(f"  Run {run+1}: Mean={mean:.6f}, Std={std:.6f}, Sum={sum_val:.6f}")
            
            # Check if embeddings are identical across runs (they should be)
            embeddings_identical = all(np.allclose(embeddings_list[0], emb) for emb in embeddings_list[1:])
            print(f"🔄 Embeddings consistent across runs: {'✅ YES' if embeddings_identical else '❌ NO'}")
            
            # Test 3: Classification results multiple times
            print(f"\n🎯 CLASSIFICATION TESTS:")
            classification_results = []
            for run in range(3):
                result, confidence, class_probs = classify_image_direct_onnx(image_path, return_confidence=True)
                classification_results.append((result, confidence))
                print(f"  Run {run+1}: {result} ({confidence:.1%})")
            
            # Check if classifications are identical (they should be for same image)
            classifications_identical = all(r[0] == classification_results[0][0] and abs(r[1] - classification_results[0][1]) < 0.001 for r in classification_results[1:])
            print(f"🎯 Classifications consistent: {'✅ YES' if classifications_identical else '❌ NO'}")
            
            # Store final result for comparison
            final_result = classification_results[0]
            print(f"📊 Final result: {final_result[0]} ({final_result[1]:.1%})")
        
        print(f"\n" + "=" * 60)
        print("💡 DIAGNOSIS:")
        print("- If embeddings are consistent within same image: ✅ ONNX model working correctly")
        print("- If classifications are consistent within same image: ✅ Classification logic working correctly")
        print("- If different images show different results: ✅ No caching between images")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_gui_display_refresh():
    """Test GUI display refresh mechanisms"""
    print("\n🖥️ TESTING GUI DISPLAY REFRESH")
    print("=" * 60)
    
    try:
        # Import GUI components
        from PyQt5.QtWidgets import QApplication, QTextEdit
        from PyQt5.QtCore import Qt
        import sys
        
        # Create minimal test app
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test QTextEdit refresh behavior
        text_widget = QTextEdit()
        
        print("📝 Testing QTextEdit refresh behavior...")
        
        # Test 1: Set initial text
        initial_text = "Initial threat analysis result"
        text_widget.setPlainText(initial_text)
        print(f"  Set initial text: '{initial_text[:30]}...'")
        
        # Test 2: Update text
        updated_text = "Updated threat analysis result - completely different"
        text_widget.setPlainText(updated_text)
        print(f"  Updated text: '{updated_text[:30]}...'")
        
        # Test 3: Force refresh
        text_widget.update()
        text_widget.repaint()
        print(f"  Forced refresh with update() and repaint()")
        
        # Test 4: Check current text
        current_text = text_widget.toPlainText()
        print(f"  Current text: '{current_text[:30]}...'")
        
        refresh_working = current_text == updated_text
        print(f"🔄 Text refresh working: {'✅ YES' if refresh_working else '❌ NO'}")
        
        print("✅ GUI display refresh test completed")
        
    except Exception as e:
        print(f"❌ GUI test error: {e}")

def test_memory_caching():
    """Test for memory-based caching issues"""
    print("\n🧠 TESTING MEMORY CACHING")
    print("=" * 60)
    
    try:
        import gc
        import sys
        
        # Check for cached modules
        cached_modules = [name for name in sys.modules.keys() if 'classifier' in name.lower()]
        print(f"📦 Cached classifier modules: {cached_modules}")
        
        # Force garbage collection
        print("🗑️ Running garbage collection...")
        collected = gc.collect()
        print(f"  Collected {collected} objects")
        
        # Check memory usage (basic)
        import psutil
        import os
        process = psutil.Process(os.getpid())
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"💾 Current memory usage: {memory_mb:.1f} MB")
        
        print("✅ Memory caching test completed")
        
    except Exception as e:
        print(f"❌ Memory test error: {e}")

def main():
    """Run all diagnostic tests"""
    print("🇮🇱 ANTI-REDFOR DETECTION SYSTEM - CACHING DIAGNOSTIC")
    print("=" * 70)
    
    # Test 1: Classification caching
    test_classification_caching()
    
    # Test 2: GUI display refresh
    test_gui_display_refresh()
    
    # Test 3: Memory caching
    test_memory_caching()
    
    print(f"\n" + "=" * 70)
    print("🔍 DIAGNOSTIC COMPLETED")
    print("Check the results above to identify caching issues.")
    print("=" * 70)

if __name__ == "__main__":
    main()
