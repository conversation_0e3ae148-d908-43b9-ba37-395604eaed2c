#!/usr/bin/env python3
"""
Simple test to check if the classification fix works
"""

import sys
from pathlib import Path
import numpy as np

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

def test_embedding_classification():
    """Test the embedding classification directly"""
    print("🧪 Testing embedding classification...")
    
    try:
        from classifier.classifier import classify_from_embeddings
        
        # Create two different test embeddings
        print("\n📊 Creating test embeddings...")
        
        # Embedding 1: High activation
        embedding1 = np.random.normal(0.2, 0.3, 640)
        print(f"Embedding 1 stats: mean={np.mean(embedding1):.4f}, std={np.std(embedding1):.4f}")
        
        # Embedding 2: Low activation  
        embedding2 = np.random.normal(0.05, 0.1, 640)
        print(f"Embedding 2 stats: mean={np.mean(embedding2):.4f}, std={np.std(embedding2):.4f}")
        
        # Classify both
        print("\n🔍 Classifying embeddings...")
        
        weapon1, conf1, probs1 = classify_from_embeddings(embedding1)
        print(f"Embedding 1 → {weapon1} ({conf1:.1%})")
        
        weapon2, conf2, probs2 = classify_from_embeddings(embedding2)  
        print(f"Embedding 2 → {weapon2} ({conf2:.1%})")
        
        # Check if they're different
        if weapon1 != weapon2:
            print("✅ SUCCESS: Different embeddings produced different results!")
        else:
            print("⚠️  WARNING: Same weapon detected for different embeddings")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_image_classification():
    """Test image classification if possible"""
    print("\n📷 Testing image classification...")
    
    try:
        from classifier.classifier import classify_image
        
        # Check if we have test images
        test_images = list(Path("data/input_images").glob("*.png")) + list(Path("data/input_images").glob("*.jpg"))
        
        if len(test_images) < 1:
            print("❌ No test images found in data/input_images/")
            return False
            
        print(f"Found {len(test_images)} test images")
        
        # Test the first image
        image_path = test_images[0]
        print(f"Testing: {image_path.name}")
        
        result, confidence, class_probs = classify_image(str(image_path), return_confidence=True)
        print(f"Result: {result} ({confidence:.1%})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🇮🇱 Simple Classification Test")
    print("=" * 50)
    
    # Test embedding classification
    success1 = test_embedding_classification()
    
    # Test image classification
    success2 = test_image_classification()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ All tests passed!")
    elif success1:
        print("⚠️  Embedding test passed, image test failed")
    else:
        print("❌ Tests failed")
    print("=" * 50)
