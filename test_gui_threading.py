#!/usr/bin/env python3
"""
Test GUI threading and result handling
"""

import sys
from pathlib import Path
import time

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

def test_analysis_thread():
    """Test the AnalysisThread directly"""
    print("🧵 TESTING ANALYSIS THREAD")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QEventLoop
        from gui_main import AnalysisThread
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test images
        test_images = [
            "data/input_images/Shahed_136_0610152327_texture.png",
            "data/input_images/test.jpg"
        ]
        
        results_received = []
        
        def on_analysis_complete(results):
            print(f"✅ Thread result: {results['weapon']} ({results['confidence']:.1%}) for {results['image_name']}")
            results_received.append(results)
        
        def on_analysis_error(error):
            print(f"❌ Thread error: {error}")
        
        for i, image_path in enumerate(test_images):
            if not Path(image_path).exists():
                print(f"❌ Image not found: {image_path}")
                continue
                
            print(f"\n🔍 Testing thread {i+1}: {Path(image_path).name}")
            
            # Create thread
            thread = AnalysisThread(image_path, "models/3N66.onnx")
            thread.analysis_complete.connect(on_analysis_complete)
            thread.analysis_error.connect(on_analysis_error)
            
            # Start thread and wait for completion
            thread.start()
            
            # Wait for thread to complete
            loop = QEventLoop()
            thread.finished.connect(loop.quit)
            loop.exec_()
            
            print(f"  Thread finished for {Path(image_path).name}")
            
            # Small delay between tests
            time.sleep(1)
        
        print(f"\n📊 THREAD TEST RESULTS:")
        print(f"  Total threads run: {len(test_images)}")
        print(f"  Results received: {len(results_received)}")
        
        if len(results_received) >= 2:
            different_weapons = results_received[0]['weapon'] != results_received[1]['weapon']
            different_confidence = abs(results_received[0]['confidence'] - results_received[1]['confidence']) > 0.01
            
            print(f"  Different weapons: {'✅ YES' if different_weapons else '❌ NO'}")
            print(f"  Different confidence: {'✅ YES' if different_confidence else '❌ NO'}")
            
            for j, result in enumerate(results_received):
                print(f"    Result {j+1}: {result['weapon']} ({result['confidence']:.1%})")
        
        print("✅ Thread testing completed")
        
    except Exception as e:
        print(f"❌ Thread test error: {e}")
        import traceback
        traceback.print_exc()

def test_module_reloading():
    """Test module reloading functionality"""
    print("\n🔄 TESTING MODULE RELOADING")
    print("=" * 50)
    
    try:
        import sys
        import importlib
        
        # Check current classifier modules
        classifier_modules = [name for name in sys.modules.keys() if 'classifier' in name.lower()]
        print(f"📦 Current classifier modules: {classifier_modules}")
        
        # Test classification before reload
        from classifier.classifier import classify_image
        result1, conf1, _ = classify_image("data/input_images/Shahed_136_0610152327_texture.png", return_confidence=True)
        print(f"🔍 Before reload: {result1} ({conf1:.1%})")
        
        # Force reload
        print("🔄 Reloading modules...")
        for module_name in classifier_modules:
            if module_name in sys.modules:
                try:
                    importlib.reload(sys.modules[module_name])
                    print(f"  ✅ Reloaded: {module_name}")
                except Exception as e:
                    print(f"  ❌ Failed to reload {module_name}: {e}")
        
        # Test classification after reload
        from classifier.classifier import classify_image
        result2, conf2, _ = classify_image("data/input_images/Shahed_136_0610152327_texture.png", return_confidence=True)
        print(f"🔍 After reload: {result2} ({conf2:.1%})")
        
        # Check if results are consistent (they should be for same image)
        consistent = result1 == result2 and abs(conf1 - conf2) < 0.01
        print(f"🎯 Results consistent: {'✅ YES' if consistent else '❌ NO'}")
        
        print("✅ Module reloading test completed")
        
    except Exception as e:
        print(f"❌ Module reload test error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all threading tests"""
    print("🇮🇱 GUI THREADING DIAGNOSTIC")
    print("=" * 60)
    
    # Test 1: Analysis thread
    test_analysis_thread()
    
    # Test 2: Module reloading
    test_module_reloading()
    
    print(f"\n" + "=" * 60)
    print("🔍 THREADING DIAGNOSTIC COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    main()
