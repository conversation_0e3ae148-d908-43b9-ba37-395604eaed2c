#!/usr/bin/env python3
"""
Test script to verify config.yaml loading fix
"""

import sys
import os
from pathlib import Path

def test_config_loading():
    """Test if config loading works in both development and .exe environments"""
    print("Testing Config Loading Fix...")
    print("=" * 50)
    
    try:
        # Test the improved config loader
        from config import config_loader
        
        print("Successfully imported config_loader")

        # Test loading config
        print("\nTesting config loading...")
        config = config_loader.load_config()

        if config:
            print("Config loaded successfully!")
            print(f"Config keys: {list(config.keys())}")

            # Test paths section
            if 'paths' in config:
                print("Paths section found")
                paths = config['paths']
                print(f"Available paths: {list(paths.keys())}")

                # Test specific paths
                onnx_model = paths.get('onnx_model', 'Not found')
                print(f"ONNX model path: {onnx_model}")

            else:
                print("No paths section in config")

            # Test weapon classes
            if 'weapon_classes' in config:
                print("Weapon classes section found")
                weapon_classes = config['weapon_classes']
                print(f"Weapon categories: {list(weapon_classes.keys())}")
            else:
                print("No weapon_classes section in config")

        else:
            print("Config is None or empty")
            return False

    except Exception as e:
        print(f"Error testing config: {e}")
        import traceback
        traceback.print_exc()
        return False

    print("\n" + "=" * 50)
    print("Config loading test completed successfully!")
    return True

def test_path_resolution():
    """Test path resolution for different environments"""
    print("\nTesting Path Resolution...")
    print("-" * 30)

    try:
        from config.config_loader import get_resource_path

        # Test various paths
        test_paths = [
            'config/config.yaml',
            'models/3N66.onnx',
            'assets/icon.ico'
        ]

        for path in test_paths:
            resolved = get_resource_path(path)
            exists = os.path.exists(resolved)
            status = "EXISTS" if exists else "NOT FOUND"
            print(f"{status} {path} -> {resolved}")

    except Exception as e:
        print(f"Error testing path resolution: {e}")
        return False

    return True

def main():
    """Main test function"""
    print("Military Threat Detection System - Config Fix Test")
    print("Testing config.yaml loading for .exe compatibility")
    print()

    # Show environment info
    print(f"Python: {sys.version.split()[0]}")
    print(f"Working directory: {os.getcwd()}")
    print(f"Running from: {'PyInstaller .exe' if getattr(sys, 'frozen', False) else 'Development'}")
    print()

    # Run tests
    success = True

    if not test_config_loading():
        success = False

    if not test_path_resolution():
        success = False

    print("\n" + "=" * 60)
    if success:
        print("ALL TESTS PASSED!")
        print("Config loading fix is working correctly")
        print("Ready to build .exe with working config!")
    else:
        print("SOME TESTS FAILED!")
        print("Please check the errors above")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
