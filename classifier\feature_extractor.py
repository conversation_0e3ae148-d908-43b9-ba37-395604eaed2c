import os
import numpy as np
import onnxruntime as ort
from PIL import Image
from pathlib import Path
from config import config_loader
import torchvision.transforms as transforms

# Load model path from config
config = config_loader.load_config()
MODEL_PATH = config['paths']['onnx_model']

# Load ONNX model with enhanced error handling for Windows DLL issues
def load_onnx_model():
    if not os.path.exists(MODEL_PATH):
        print(f"Warning: ONNX model not found at: {MODEL_PATH}")
        print("Please run with --select-model to choose a valid model")
        return None

    try:
        # Try different execution providers in order of preference
        providers = [
            'CPUExecutionProvider',
            'DmlExecutionProvider',  # DirectML for Windows
        ]

        # Filter available providers
        available_providers = ort.get_available_providers()
        filtered_providers = [p for p in providers if p in available_providers]

        if not filtered_providers:
            filtered_providers = ['CPUExecutionProvider']  # Fallback

        print(f"[INFO] Using ONNX providers: {filtered_providers}")

        # Create session with specific options for Windows compatibility
        session_options = ort.SessionOptions()
        session_options.enable_cpu_mem_arena = False  # Helps with DLL issues
        session_options.enable_mem_pattern = False    # Reduces memory fragmentation

        session = ort.InferenceSession(
            MODEL_PATH,
            providers=filtered_providers,
            sess_options=session_options
        )

        print(f"[INFO] Successfully loaded ONNX model: {MODEL_PATH}")
        return session

    except Exception as e:
        print(f"❌ Failed to load ONNX model: {e}")
        print("💡 Troubleshooting:")
        print("  1. Install Visual C++ Redistributable 2019-2022")
        print("  2. Try: pip uninstall onnxruntime && pip install onnxruntime==1.16.3")
        print("  3. Restart your computer after installation")
        print("  4. Check if antivirus is blocking DLL files")
        return None

session = load_onnx_model()
if session:
    input_name = session.get_inputs()[0].name
else:
    input_name = None

# Image preprocessing to match ViT-B-16 expectations
transform = transforms.Compose([
    transforms.Resize((240, 240)),  # ViT-B-16-plus-240 expects 240x240
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.5]*3, std=[0.5]*3)
])

def extract_features(image_path):
    if session is None:
        raise RuntimeError(
            "❌ ONNX model not loaded. Please select a valid model first.\n"
            "💡 Try: python main.py --select-model"
        )

    try:
        img = Image.open(image_path).convert("RGB")
        print(f"[INFO] Successfully loaded image: {os.path.basename(image_path)}")
    except Exception as e:
        raise IOError(f"❌ Failed to open image {image_path}: {e}")

    try:
        img_tensor = transform(img).unsqueeze(0).numpy()
        print(f"[INFO] Image preprocessed to shape: {img_tensor.shape}")
    except Exception as e:
        raise RuntimeError(f"❌ Image preprocessing failed: {e}")

    try:
        print(f"[INFO] Running ONNX inference...")
        outputs = session.run(None, {input_name: img_tensor})
        embedding = outputs[0].squeeze()
        print(f"[INFO] Feature extraction successful, embedding shape: {embedding.shape}")
        return embedding
    except Exception as e:
        error_msg = str(e)
        if "DLL" in error_msg or "pybind11" in error_msg:
            raise RuntimeError(
                f"❌ ONNX Runtime DLL Error: {e}\n\n"
                "💡 Windows DLL Fix:\n"
                "1. Install Visual C++ Redistributable 2019-2022\n"
                "2. Run: pip uninstall onnxruntime && pip install onnxruntime==1.16.3\n"
                "3. Restart your computer\n"
                "4. Check antivirus settings\n\n"
                "🔗 Download VC++ Redistributable:\n"
                "https://aka.ms/vs/17/release/vc_redist.x64.exe"
            )
        else:
            raise RuntimeError(f"❌ Feature extraction failed: {e}")
